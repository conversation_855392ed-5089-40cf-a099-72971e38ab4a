#!/bin/bash

# JianfaAI 启动脚本
# 确保所有必要的目录和文件存在

echo "======================================="
echo "        JianfaAI 启动脚本"
echo "======================================="

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "当前工作目录: $(pwd)"

# 运行环境检查
echo "运行环境检查..."
if ! node check-env.js; then
    echo "❌ 环境检查失败，请修复问题后重试"
    exit 1
fi

# 创建必要的目录
echo "创建必要的目录..."
mkdir -p logs
mkdir -p public/file
mkdir -p public/uploads

echo "✅ 目录结构检查完成"

# 检查端口占用
PORT=$(grep "^PORT=" .env | cut -d'=' -f2 | tr -d ' ')
if [ -z "$PORT" ]; then
    PORT=9520
fi

echo "检查端口 $PORT 是否被占用..."
if netstat -tlnp | grep ":$PORT " > /dev/null; then
    echo "⚠️  警告: 端口 $PORT 已被占用"
    echo "正在查找占用进程..."
    netstat -tlnp | grep ":$PORT "
fi

# 停止现有的PM2进程
echo "停止现有的PM2进程..."
pm2 delete JianfaAI 2>/dev/null || echo "没有找到现有的JianfaAI进程"

# 清理PM2日志
echo "清理PM2日志..."
pm2 flush

# 启动服务
echo "启动JianfaAI服务..."
pm2 start pm2.conf.json

# 等待启动
echo "等待服务启动..."
sleep 5

# 检查状态
echo "检查服务状态..."
pm2 status

echo "======================================="
echo "启动完成！"
echo "服务地址: http://localhost:$PORT"
echo "查看日志: pm2 logs JianfaAI"
echo "======================================="
