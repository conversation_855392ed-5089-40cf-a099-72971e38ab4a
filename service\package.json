{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "4.3.0", "description": "", "author": "vastxie", "private": true, "license": "Apache-2.0", "bin": "./dist/main.js", "scripts": {"start": "pm2 start pm2.conf.json", "prebuild": "pnpm run format", "build": "pnpm format && nest build", "build:test": "nest build", "format": "prettier --write 'src/**/*.{vue,ts,tsx,js,jsx,css,scss,less}'", "encrypt": "node ./encrypt.js", "start:daemon": "pm2 start pm2.conf.json --no-daemon", "dev": "nest start --watch --preserveWatchOutput", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "pkg:win": "pkg . -t node16-win-x64 -o app-win --debug", "pkg:mac": "pkg . -t node16-mac-x64 -o app-mac --debug", "pkg:linux": "pkg . -t node16-linux-x64 -o app-linux --debug"}, "dependencies": {"@alicloud/pop-core": "^1.8.0", "@google/genai": "^0.10.0", "@langchain/community": "^0.3.42", "@langchain/core": "^0.3.55", "@langchain/langgraph": "^0.2.72", "@langchain/langgraph-sdk": "^0.0.74", "@langchain/openai": "^0.5.10", "@langchain/tavily": "^0.1.1", "@modelcontextprotocol/sdk": "^1.10.1", "@nestjs/common": "^10.4.17", "@nestjs/core": "^10.4.17", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.17", "@nestjs/schedule": "^4.1.2", "@nestjs/serve-static": "^4.0.2", "@nestjs/swagger": "^7.4.2", "@nestjs/typeorm": "^10.0.2", "@nestjs/websockets": "^10.4.17", "@types/raw-body": "^2.3.0", "abort-controller": "^3.0.0", "ali-oss": "^6.22.0", "axios": "^1.8.4", "bcryptjs": "^2.4.3", "body-parser": "^1.20.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "compression": "^1.8.0", "cos-nodejs-sdk-v5": "^2.14.7", "cross-fetch": "3.1.6", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "exceljs": "^4.4.0", "express": "^4.21.2", "fast-xml-parser": "^5.2.0", "form-data": "^4.0.2", "gpt-tokenizer": "^2.9.0", "guid-typescript": "^1.0.9", "https-proxy-agent": "7.0.2", "iconv-lite": "^0.6.3", "ioredis": "^5.6.1", "jschardet": "^3.1.4", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mammoth": "^1.9.0", "markdown-table": "^3.0.4", "mime-types": "^2.1.35", "mysql2": "^3.14.0", "node-cron": "^3.0.3", "node-machine-id": "^1.1.12", "nodemailer": "^6.10.1", "openai": "^4.96.0", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "path-to-regexp": "^1.9.0", "pdf-parse": "^1.1.1", "pm2": "^6.0.5", "pptxtojson": "^1.3.1", "raw-body": "^3.0.0", "redis": "^4.7.0", "reflect-metadata": "^0.1.14", "rxjs": "^7.8.2", "stream-to-buffer": "^0.1.0", "tough-cookie": "^4.1.3", "typeorm": "^0.3.22", "uuid": "^9.0.1", "wechatpay-node-v3": "^2.2.1", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.2/xlsx-0.20.2.tgz"}, "devDependencies": {"@babel/core": "^7.26.10", "@nestjs/cli": "^10.4.9", "@types/express": "^4.17.21", "@types/node": "18.11.18", "fs-extra": "^11.3.0", "javascript-obfuscator": "^4.1.1", "jest": "29.3.1", "prettier": "^2.8.8", "ts-jest": "29.0.3", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "pnpm": {"peerDependencyRules": {"ignoreMissing": ["@nestjs/websockets", "@nestjs/common", "@nestjs/core"]}, "overrides": {"@nestjs/serve-static>path-to-regexp": "^1.9.0", "tough-cookie": "^4.1.3"}}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}