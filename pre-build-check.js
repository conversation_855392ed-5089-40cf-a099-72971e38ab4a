#!/usr/bin/env node

/**
 * 构建前检查脚本
 * 检查所有可能导致构建失败的问题
 */

const fs = require('fs');
const path = require('path');

console.log('======================================');
console.log('        JianfaAI 构建前检查');
console.log('======================================\n');

let hasError = false;

// 检查Node.js版本
function checkNodeVersion() {
  console.log('🔍 检查Node.js版本...');
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  if (majorVersion < 16) {
    console.error(`❌ Node.js版本过低: ${nodeVersion} (需要 >= 16)`);
    hasError = true;
  } else {
    console.log(`✅ Node.js版本: ${nodeVersion}`);
  }
}

// 检查pnpm
function checkPnpm() {
  console.log('\n🔍 检查pnpm...');
  try {
    const { execSync } = require('child_process');
    const pnpmVersion = execSync('pnpm --version', { encoding: 'utf8' }).trim();
    console.log(`✅ pnpm版本: ${pnpmVersion}`);
  } catch (error) {
    console.error('❌ pnpm未安装或不可用');
    console.error('请运行: npm install -g pnpm');
    hasError = true;
  }
}

// 检查项目结构
function checkProjectStructure() {
  console.log('\n📁 检查项目结构...');
  
  const requiredDirs = [
    'admin',
    'chat', 
    'service',
    'AIWebQuickDeploy'
  ];
  
  const requiredFiles = [
    'admin/package.json',
    'chat/package.json',
    'service/package.json',
    'service/src/main.ts',
    'build.sh'
  ];
  
  // 检查目录
  for (const dir of requiredDirs) {
    if (!fs.existsSync(dir)) {
      console.error(`❌ 缺少目录: ${dir}`);
      hasError = true;
    } else {
      console.log(`✅ 目录存在: ${dir}`);
    }
  }
  
  // 检查文件
  for (const file of requiredFiles) {
    if (!fs.existsSync(file)) {
      console.error(`❌ 缺少文件: ${file}`);
      hasError = true;
    } else {
      console.log(`✅ 文件存在: ${file}`);
    }
  }
}

// 检查package.json依赖
function checkDependencies() {
  console.log('\n📦 检查依赖配置...');
  
  const packages = ['admin', 'chat', 'service'];
  
  for (const pkg of packages) {
    const packageJsonPath = path.join(pkg, 'package.json');
    if (fs.existsSync(packageJsonPath)) {
      try {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        
        // 检查必要的脚本
        if (!packageJson.scripts || !packageJson.scripts.build) {
          console.error(`❌ ${pkg}/package.json 缺少 build 脚本`);
          hasError = true;
        } else {
          console.log(`✅ ${pkg} 构建脚本存在`);
        }
        
        // 检查依赖
        if (!packageJson.dependencies && !packageJson.devDependencies) {
          console.warn(`⚠️  ${pkg} 没有依赖项`);
        }
        
      } catch (error) {
        console.error(`❌ ${pkg}/package.json 格式错误: ${error.message}`);
        hasError = true;
      }
    }
  }
}

// 检查TypeScript配置
function checkTypeScriptConfig() {
  console.log('\n🔧 检查TypeScript配置...');
  
  const tsConfigs = [
    'service/tsconfig.json',
    'service/tsconfig.build.json',
    'admin/tsconfig.json',
    'chat/tsconfig.json'
  ];
  
  for (const config of tsConfigs) {
    if (fs.existsSync(config)) {
      try {
        const tsConfig = JSON.parse(fs.readFileSync(config, 'utf8'));
        console.log(`✅ ${config} 配置正确`);
      } catch (error) {
        console.error(`❌ ${config} 格式错误: ${error.message}`);
        hasError = true;
      }
    } else {
      console.warn(`⚠️  ${config} 不存在`);
    }
  }
}

// 检查环境变量模板
function checkEnvTemplate() {
  console.log('\n🌍 检查环境变量模板...');
  
  const envFiles = [
    'service/.env.example'
  ];
  
  for (const envFile of envFiles) {
    if (fs.existsSync(envFile)) {
      console.log(`✅ ${envFile} 存在`);
    } else {
      console.warn(`⚠️  ${envFile} 不存在`);
    }
  }
}

// 检查构建脚本权限
function checkBuildScript() {
  console.log('\n🔨 检查构建脚本...');
  
  if (fs.existsSync('build.sh')) {
    try {
      const stats = fs.statSync('build.sh');
      const isExecutable = !!(stats.mode & parseInt('111', 8));
      
      if (!isExecutable) {
        console.warn('⚠️  build.sh 没有执行权限');
        console.log('请运行: chmod +x build.sh');
      } else {
        console.log('✅ build.sh 有执行权限');
      }
    } catch (error) {
      console.error(`❌ 检查build.sh权限失败: ${error.message}`);
    }
  }
}

// 检查磁盘空间
function checkDiskSpace() {
  console.log('\n💾 检查磁盘空间...');
  
  try {
    const { execSync } = require('child_process');
    const dfOutput = execSync('df -h .', { encoding: 'utf8' });
    console.log('磁盘使用情况:');
    console.log(dfOutput);
  } catch (error) {
    console.warn('⚠️  无法检查磁盘空间');
  }
}

// 主检查函数
async function main() {
  try {
    checkNodeVersion();
    checkPnpm();
    checkProjectStructure();
    checkDependencies();
    checkTypeScriptConfig();
    checkEnvTemplate();
    checkBuildScript();
    checkDiskSpace();
    
    console.log('\n======================================');
    if (hasError) {
      console.log('❌ 构建前检查失败，请修复上述问题后重试');
      process.exit(1);
    } else {
      console.log('✅ 构建前检查通过，可以开始构建');
      process.exit(0);
    }
  } catch (error) {
    console.error(`❌ 检查过程中出错: ${error.message}`);
    process.exit(1);
  }
}

main();
