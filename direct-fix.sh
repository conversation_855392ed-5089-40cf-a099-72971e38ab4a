#!/bin/bash

# 直接修复脚本 - 最简单的解决方案

echo "======================================="
echo "        直接修复AWS SDK问题"
echo "======================================="

echo "🔧 开始直接修复..."

# 1. 进入service目录并重新安装
echo "📦 修复后端服务..."
cd service/

echo "🧹 清理后端依赖..."
rm -rf node_modules
rm -f pnpm-lock.yaml

echo "📦 重新安装后端依赖..."
pnpm install

echo "🔨 构建后端服务..."
pnpm build

if [ $? -eq 0 ]; then
    echo "✅ 后端服务修复成功"
else
    echo "❌ 后端服务修复失败"
    exit 1
fi

cd ..

echo ""
echo "======================================="
echo "✅ 后端服务修复完成！"
echo ""
echo "现在可以运行完整构建："
echo "  ./build.sh"
echo "======================================="
