{"name": "jian<PERSON><PERSON>-chat", "version": "1.0.0", "private": true, "description": "JianfaAI chat", "author": "vastxie", "keywords": ["JianfaAI", "chatgpt", "ChatBox", "AI助手"], "main": "electron/main.js", "scripts": {"start:h": "pnpm run -C service dev", "start:f": "vite", "all": "npm-run-all --parallel start:h start:f", "dev": "vite", "build-check": "run-p type-check build-only", "preview": "vite preview", "build": "pnpm format && vite build --mode=production", "type-check": "vue-tsc --noEmit", "bootstrap": "pnpm install && pnpm run common:prepare", "common:cleanup": "rimraf node_modules && rimraf pnpm-lock.yaml", "format": "pnpm dlx prettier --write '{src,scripts,config}/**/*.{vue,ts,tsx,js,jsx,css,scss,less,json,md,html,yml,yaml}' '*.{js,ts,json,md,yml}'", "electron": "electron .", "electron:dev": "cross-env ELECTRON_START_URL=http://localhost:9002 electron .", "electron:build": "pnpm run build && electron-builder", "electron:mac": "pnpm run build && npx electron-builder --mac --config.npmRebuild=false", "electron:mac-universal": "pnpm run build && npx electron-builder --mac --universal --config.npmRebuild=false", "electron:win": "pnpm run build && electron-builder --win", "electron:win-x64": "pnpm run build && electron-builder --win --x64", "electron:win-arm64": "pnpm run build && electron-builder --win --arm64", "electron:all": "pnpm run build && npx electron-builder --mac --universal --win --config.npmRebuild=false"}, "dependencies": {"@codemirror/lang-cpp": "^6.0.2", "@codemirror/lang-css": "^6.3.1", "@codemirror/lang-html": "^6.4.9", "@codemirror/lang-java": "^6.0.1", "@codemirror/lang-javascript": "^6.2.3", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-markdown": "^6.3.2", "@codemirror/lang-php": "^6.0.1", "@codemirror/lang-python": "^6.1.7", "@codemirror/lang-rust": "^6.0.1", "@codemirror/lang-sql": "^6.8.0", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.2", "@icon-park/vue-next": "^1.4.2", "@opendocsg/pdf2md": "^0.2.1", "@tailwindcss/typography": "^0.5.16", "@traptitech/markdown-it-katex": "^3.6.0", "@vueuse/core": "^13.1.0", "@vueuse/integrations": "^13.1.0", "@vueuse/motion": "^3.0.3", "clientjs": "^0.2.1", "codemirror": "^6.0.1", "file-saver": "^2.0.5", "highlight.js": "^11.11.1", "html-to-image": "^1.11.13", "html2pdf.js": "^0.10.3", "jschardet": "^3.1.4", "katex": "^0.16.22", "mammoth": "^1.9.0", "markdown-it": "^14.1.0", "markdown-tables": "^3.0.5", "markmap-common": "0.15.0", "markmap-lib": "0.15.0", "markmap-view": "0.15.0", "md-editor-v3": "^4.21.3", "mermaid": "^11.6.0", "npx": "^10.2.2", "office-text-extractor": "^3.0.3", "pdfjs-dist": "^5.2.133", "pinia": "^2.3.1", "pinyin-match": "^1.2.6", "pptxtojson": "^1.3.1", "qrcode": "^1.5.4", "tailwind-scrollbar": "^3.1.0", "turndown": "^7.2.0", "v-viewer": "3.0.21", "vue": "^3.5.13", "vue-i18n": "^11.1.3", "vue-router": "^4.5.0", "xlsx": "^0.18.5"}, "devDependencies": {"@iconify/vue": "^4.3.0", "@types/crypto-js": "^4.2.2", "@types/katex": "^0.16.7", "@types/markdown-table": "^3.0.0", "@types/node": "^18.19.86", "@types/turndown": "^5.0.5", "@vitejs/plugin-vue": "^5.2.3", "autoprefixer": "^10.4.21", "axios": "^1.8.4", "cross-env": "^7.0.3", "crypto-js": "^4.2.0", "less": "^4.3.0", "markdown-it-link-attributes": "^4.0.1", "npm-run-all": "^4.1.5", "prettier": "^3.5.3", "rimraf": "^4.4.1", "tailwindcss": "^3.4.17", "typescript": "~4.9.5", "vite": "^4.5.14", "vue-tsc": "^1.8.27"}, "license": "MIT"}