# 99AI 到 JianfaAI 完整替换方案

## 🎯 替换原则

**安全第一**：只替换显示相关的内容，不修改影响程序逻辑的部分
- ✅ 可以替换：标题、描述、注释、文档
- ❌ 不能替换：变量名、函数名、数据库字段、API路径

## 📋 需要替换的文件清单

### 🔴 高优先级（用户可见）

#### 1. 项目根目录文件
- `README.md` - 项目说明文档
- `99AI项目详细文档.md` - 项目文档（可删除或重命名）
- `99AI项目结构图.md` - 项目结构图（可删除或重命名）

#### 2. 前端HTML文件
- `chat/index.html` - 聊天界面标题和描述
- `AIWebQuickDeploy/public/chat/index.html` - 部署版聊天界面
- `admin/index.html` - 管理后台（已修改）
- `AIWebQuickDeploy/public/admin/index.html` - 部署版管理后台（已修改）

#### 3. 前端配置文件
- `chat/package.json` - 项目名称和描述
- `admin/package.json` - 项目名称（已修改）

#### 4. 后端显示文件
- `service/src/main.ts` - 启动日志和Swagger文档标题
- `service/package.json` - 项目名称
- `AIWebQuickDeploy/package.json` - 部署版项目名称

#### 5. PM2配置文件
- `service/pm2.conf.json` - 进程名称
- `AIWebQuickDeploy/pm2.conf.json` - 部署版进程名称

### 🟡 中优先级（开发相关）

#### 6. VSCode配置文件
- `.vscode/settings.json` - 拼写检查词典
- `service/.vscode/settings.json` - 拼写检查词典

#### 7. 用户相关代码
- `chat/src/components/Settings/AccountManagement.vue` - 邮箱域名排除列表

### 🟢 低优先级（可选）

#### 8. 文档文件
- `修改说明.md` - 修改记录文档
- `actualOpenaiBaseUrl参数说明与修改记录.md` - 参数说明文档

## 🛠️ 具体替换内容

### 1. README.md
```markdown
# 修改前
🚀 99AI -- 一站式 AI 服务平台
99AI 是一个**可商业化的 AI Web 平台**

# 修改后  
🚀 JianfaAI -- 一站式 AI 服务平台
JianfaAI 是一个**可商业化的 AI Web 平台**
```

### 2. chat/index.html
```html
<!-- 修改前 -->
<title>AIWeb</title>
<meta name="description" content="AIWeb 是一个集成化的人工智能服务站点">

<!-- 修改后 -->
<title>JianfaAI</title>
<meta name="description" content="JianfaAI 是一个集成化的人工智能服务站点">
```

### 3. service/src/main.ts
```typescript
// 修改前
console.log('        99AI 服务启动中...            ');
.setTitle('99AI API')
.setDescription('99AI服务API文档')

// 修改后
console.log('        JianfaAI 服务启动中...            ');
.setTitle('JianfaAI API')
.setDescription('JianfaAI服务API文档')
```

### 4. package.json文件
```json
// 修改前
"name": "99ai"
"description": "99AI chat"

// 修改后
"name": "jianfaai"
"description": "JianfaAI chat"
```

### 5. PM2配置
```json
// 修改前
"name": "99AI"

// 修改后
"name": "JianfaAI"
```

## ⚠️ 重要注意事项

### 不能修改的内容
1. **数据库相关**：数据库名、表名、字段名
2. **API路径**：不要修改任何API端点路径
3. **环境变量**：配置文件中的变量名
4. **文件路径**：代码中引用的文件路径
5. **CSS类名**：样式相关的类名和ID

### 安全替换规则
1. **只替换显示文本**：标题、描述、注释
2. **保持版本号**：可以修改，但要保持格式一致
3. **保持结构**：不改变JSON、HTML的结构
4. **测试验证**：每次修改后都要测试功能

## 🔄 替换顺序建议

### 第一阶段：核心显示
1. HTML文件标题和描述
2. 启动日志信息
3. Swagger文档标题

### 第二阶段：配置文件
1. package.json项目名称
2. PM2进程名称
3. 项目文档

### 第三阶段：开发文件
1. VSCode配置
2. 注释和文档
3. 其他非关键文件

## 📝 替换后验证清单

- [ ] 前端页面正常加载
- [ ] 后端服务正常启动
- [ ] 数据库连接正常
- [ ] API接口正常响应
- [ ] 用户登录功能正常
- [ ] 聊天功能正常
- [ ] 管理后台正常

## 🚀 执行建议

1. **备份项目**：替换前完整备份
2. **分批替换**：按优先级分批进行
3. **逐步测试**：每个阶段后都要测试
4. **回滚准备**：出现问题立即回滚

这个方案确保了品牌替换的同时，不会影响程序的正常运行。
