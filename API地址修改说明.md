# API地址修改说明

## 🎯 问题描述

在后台管理界面的"模型管理" → "基础配置"中，显示：
```
最终调用地址：https://api.lightai.io/v1
```

需要修改为：
```
最终调用地址：https://api.jianfaai.com/v1
```

## 🔍 问题分析

这个显示的地址来源于：
1. **数据库存储的值**：当前数据库中存储的 `openaiBaseUrl` 配置
2. **前端计算属性**：`actualOpenaiBaseUrl` 会自动在地址后添加 `/v1`

## ✅ 解决方案

### 方案一：通过后台界面修改（推荐）

1. **登录后台管理**
2. **进入模型管理**：导航到 "模型管理" → "基础配置"
3. **修改全局地址**：
   - 找到 "全局地址" 输入框
   - 将当前值改为：`https://api.jianfaai.com`
   - 点击 "保存设置"

4. **验证结果**：
   - 输入：`https://api.jianfaai.com`
   - 显示：`最终调用地址：https://api.jianfaai.com/v1`

### 方案二：修改前端默认值

已修改前端代码中的默认值：

**文件**：`admin/src/views/models/baseSetting.vue`
**修改位置**：第181行

```javascript
// 修改前
openaiBaseUrl = '',

// 修改后  
openaiBaseUrl = 'https://api.jianfaai.com',
```

**作用**：当数据库中没有配置值时，会使用这个默认值。

## 🔧 工作原理

### URL自动处理机制

系统有一个 `correctApiBaseUrl` 函数，会自动处理输入的URL：

```javascript
const correctApiBaseUrl = (baseUrl: string): string => {
  if (!baseUrl) return '';
  
  let url = baseUrl.trim();
  
  // 去除末尾斜杠
  if (url.endsWith('/')) {
    url = url.slice(0, -1);
  }
  
  // 如果没有版本号，自动添加 /v1
  if (!/\/v\d+(?:beta|alpha)?/.test(url)) {
    return `${url}/v1`;
  }
  
  return url;
};
```

### 处理示例

| 用户输入 | 系统处理后 | 显示结果 |
|---------|-----------|---------|
| `https://api.jianfaai.com` | `https://api.jianfaai.com/v1` | ✅ 正确 |
| `https://api.jianfaai.com/` | `https://api.jianfaai.com/v1` | ✅ 正确 |
| `https://api.jianfaai.com/v1` | `https://api.jianfaai.com/v1` | ✅ 正确 |

## 📋 预设选项

系统还提供了预设选项，用户可以直接选择：

```javascript
const options = [
  {
    value: 'https://api.deepseek.com',
    label: '【DeepSeek 官方】https://api.deepseek.com',
  },
  {
    value: 'https://api.jianfaai.com',
    label: '【JianfaAI 推荐】https://api.jianfaai.com',
  },
  // ... 其他选项
];
```

## 🚀 建议操作步骤

1. **重新构建前端**（如果修改了代码）：
   ```bash
   ./build.sh
   ```

2. **重启服务**：
   ```bash
   pm2 restart JianfaAI
   ```

3. **清除浏览器缓存**：
   - 按 `Ctrl+F5` 强制刷新
   - 或清除浏览器缓存

4. **验证修改**：
   - 登录后台管理
   - 进入模型管理 → 基础配置
   - 检查显示是否正确

## ⚠️ 注意事项

1. **数据优先级**：数据库中的值优先于代码中的默认值
2. **缓存问题**：修改后可能需要清除浏览器缓存
3. **API有效性**：确保新的API地址是有效的
4. **密钥配置**：修改地址后，可能需要更新对应的API密钥

## 🔍 验证方法

修改完成后，可以通过以下方式验证：

1. **界面显示**：检查 "最终调用地址" 是否显示正确
2. **功能测试**：测试AI对话功能是否正常
3. **日志检查**：查看后端日志是否有错误信息

现在您的系统将默认使用 JianfaAI 的API地址了！
