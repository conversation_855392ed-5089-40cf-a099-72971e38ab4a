#!/bin/bash

# 终极修复脚本
# 从源码根源彻底解决所有问题，确保程序能完整构建并成功运行

echo "======================================="
echo "     JianfaAI 终极修复脚本"
echo "     从源码根源彻底解决所有问题"
echo "======================================="

# 设置错误时退出
set -e

# 检查必要工具
echo "🔍 检查必要工具..."
command -v node >/dev/null 2>&1 || { echo "❌ 错误: 需要安装 Node.js"; exit 1; }
command -v pnpm >/dev/null 2>&1 || { echo "❌ 错误: 需要安装 pnpm"; exit 1; }
command -v pm2 >/dev/null 2>&1 || { echo "❌ 错误: 需要安装 pm2"; exit 1; }

echo "✅ 必要工具检查通过"

# 第一步：彻底清理
echo ""
echo "=== 第一步：彻底清理 ==="
echo "🧹 删除所有构建产物和依赖..."

# 停止所有PM2进程
pm2 stop all 2>/dev/null || true
pm2 delete all 2>/dev/null || true

# 删除构建产物和依赖
find . -name "node_modules" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name "dist" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name "pnpm-lock.yaml" -delete 2>/dev/null || true
find . -name "package-lock.json" -delete 2>/dev/null || true
find . -name "yarn.lock" -delete 2>/dev/null || true

# 清理缓存
pnpm store prune 2>/dev/null || true

echo "✅ 清理完成"

# 第二步：修复源码问题
echo ""
echo "=== 第二步：修复源码问题 ==="

# 检查并删除AWS SDK残留
echo "🔧 检查并删除AWS SDK残留..."
for dir in admin chat service; do
    if [ -f "$dir/package.json" ]; then
        if grep -q "@aws-sdk\|aws-sdk" "$dir/package.json"; then
            echo "⚠️  在 $dir/package.json 中发现AWS SDK残留，正在删除..."
            sed -i.bak '/@aws-sdk/d; /aws-sdk/d' "$dir/package.json"
            rm -f "$dir/package.json.bak"
            echo "✅ 已删除 $dir 中的AWS SDK依赖"
        fi
    fi
done

echo "✅ 源码问题修复完成"

# 第三步：重新构建
echo ""
echo "=== 第三步：重新构建 ==="

# 构建后端
echo "📦 构建后端服务..."
cd service/
pnpm install
pnpm build
[ ! -f "dist/main.js" ] && { echo "❌ 后端构建失败"; exit 1; }
echo "✅ 后端构建成功"
cd ..

# 构建管理后台
echo "📦 构建管理后台..."
cd admin/
pnpm install
pnpm build
[ ! -f "dist/index.html" ] && { echo "❌ 管理后台构建失败"; exit 1; }
echo "✅ 管理后台构建成功"
cd ..

# 构建用户界面
echo "📦 构建用户界面..."
cd chat/
pnpm install
pnpm build
[ ! -f "dist/index.html" ] && { echo "❌ 用户界面构建失败"; exit 1; }
echo "✅ 用户界面构建成功"
cd ..

# 第四步：准备部署
echo ""
echo "=== 第四步：准备部署 ==="

echo "📁 准备部署目录..."
rm -rf ./AIWebQuickDeploy/dist/* ./AIWebQuickDeploy/public/* 2>/dev/null || true

# 创建目录结构
mkdir -p ./AIWebQuickDeploy/dist
mkdir -p ./AIWebQuickDeploy/public/admin
mkdir -p ./AIWebQuickDeploy/public/chat
mkdir -p ./AIWebQuickDeploy/public/file
mkdir -p ./AIWebQuickDeploy/public/uploads
mkdir -p ./AIWebQuickDeploy/logs

# 复制文件
echo "📄 复制配置文件..."
cp service/package.json ./AIWebQuickDeploy/package.json
cp service/pm2.conf.json ./AIWebQuickDeploy/pm2.conf.json
[ -f service/.env.example ] && cp service/.env.example ./AIWebQuickDeploy/.env.example

echo "📦 复制构建产物..."
cp -a service/dist/* ./AIWebQuickDeploy/dist/
cp -r admin/dist/* ./AIWebQuickDeploy/public/admin/
cp -r chat/dist/* ./AIWebQuickDeploy/public/chat/

echo "✅ 部署准备完成"

# 第五步：配置运行环境
echo ""
echo "=== 第五步：配置运行环境 ==="

cd AIWebQuickDeploy/

echo "📦 安装运行依赖..."
pnpm install --prod

echo "📄 配置环境文件..."
if [ ! -f ".env" ]; then
    if [ -f ".env.example" ]; then
        cp .env.example .env
        echo "✅ 已创建 .env 文件"
    else
        echo "⚠️  .env.example 不存在，请手动创建 .env 文件"
    fi
fi

# 第六步：启动测试
echo ""
echo "=== 第六步：启动测试 ==="

echo "🧪 测试应用启动..."
timeout 10s node dist/main.js > test.log 2>&1 &
TEST_PID=$!
sleep 5

if kill -0 $TEST_PID 2>/dev/null; then
    echo "✅ 应用可以正常启动"
    kill $TEST_PID 2>/dev/null || true
    wait $TEST_PID 2>/dev/null || true
else
    echo "⚠️  应用启动测试结果不确定"
    echo "可能需要配置数据库连接"
fi

rm -f test.log

# 第七步：启动服务
echo ""
echo "=== 第七步：启动服务 ==="

echo "🚀 启动PM2服务..."
pm2 start pm2.conf.json

sleep 3

echo "📊 检查服务状态..."
pm2 status

# 检查服务是否正常运行
if pm2 list | grep -q "online"; then
    echo "✅ 服务启动成功"
else
    echo "⚠️  服务可能未正常启动，请检查日志"
    pm2 logs JianfaAI --lines 20 --nostream
fi

cd ..

# 完成
echo ""
echo "======================================="
echo "✅ 终极修复完成！"
echo "======================================="
echo ""
echo "📋 完成的操作："
echo "  ✅ 彻底清理所有构建产物和依赖"
echo "  ✅ 修复AWS SDK残留问题"
echo "  ✅ 重新构建所有项目"
echo "  ✅ 准备完整部署包"
echo "  ✅ 配置运行环境"
echo "  ✅ 启动PM2服务"
echo ""
echo "🌐 访问地址："
echo "  前端用户界面: http://localhost:9520"
echo "  管理后台: http://localhost:9520/admin"
echo ""
echo "🔧 管理命令："
echo "  cd AIWebQuickDeploy"
echo "  pm2 status          # 查看状态"
echo "  pm2 logs JianfaAI   # 查看日志"
echo "  pm2 restart JianfaAI # 重启服务"
echo "  pm2 stop JianfaAI   # 停止服务"
echo ""
echo "📝 默认管理员账号："
echo "  用户名: super"
echo "  密码: 123456"
echo ""
echo "⚠️  如果服务未正常启动，请："
echo "  1. 检查 AIWebQuickDeploy/.env 数据库配置"
echo "  2. 确保数据库服务正在运行"
echo "  3. 查看详细日志: pm2 logs JianfaAI"
echo "======================================="
