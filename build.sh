#!/bin/bash

# JianfaAI 项目构建脚本
# 确保构建过程的稳定性和错误处理

set -e  # 遇到错误立即退出

echo "======================================="
echo "        JianfaAI 项目构建开始"
echo "======================================="

# 运行构建前检查
if [ -f "pre-build-check.js" ]; then
    echo "🔍 运行构建前检查..."
    if ! node pre-build-check.js; then
        echo "❌ 构建前检查失败，请修复问题后重试"
        exit 1
    fi
else
    # 基本环境检查
    command -v pnpm >/dev/null 2>&1 || { echo "❌ 错误: 需要安装 pnpm"; exit 1; }
    command -v node >/dev/null 2>&1 || { echo "❌ 错误: 需要安装 Node.js"; exit 1; }
    echo "✅ 基本环境检查通过"
fi

# 构建管理后台
echo "📦 构建管理后台..."
cd admin/
if [ ! -f "package.json" ]; then
    echo "❌ 错误: admin/package.json 不存在"
    exit 1
fi

# 智能处理依赖安装
echo "🔍 安装管理后台依赖..."
if ! pnpm install --frozen-lockfile 2>/dev/null; then
    echo "⚠️  重新生成依赖锁定文件..."
    rm -f pnpm-lock.yaml
    pnpm install
fi

pnpm build
echo "✅ 管理后台构建完成"
cd ..

# 构建用户界面
echo "📦 构建用户界面..."
cd chat/
if [ ! -f "package.json" ]; then
    echo "❌ 错误: chat/package.json 不存在"
    exit 1
fi

# 智能处理依赖安装
echo "🔍 安装用户界面依赖..."
if ! pnpm install --frozen-lockfile 2>/dev/null; then
    echo "⚠️  重新生成依赖锁定文件..."
    rm -f pnpm-lock.yaml
    pnpm install
fi

pnpm build
echo "✅ 用户界面构建完成"
cd ..

# 构建后端服务
echo "📦 构建后端服务..."
cd service/
if [ ! -f "package.json" ]; then
    echo "❌ 错误: service/package.json 不存在"
    exit 1
fi

# 检查lockfile是否与package.json匹配
echo "🔍 检查依赖锁定文件..."
if pnpm install --frozen-lockfile 2>/dev/null; then
    echo "✅ 依赖锁定文件匹配"
else
    echo "⚠️  依赖锁定文件不匹配，重新生成..."
    # 删除旧的lockfile和node_modules
    rm -f pnpm-lock.yaml
    rm -rf node_modules
    # 重新安装依赖
    pnpm install
fi

pnpm build
echo "✅ 后端服务构建完成"
cd ..

# 准备部署目录
echo "📁 准备部署目录..."
rm -rf ./AIWebQuickDeploy/dist/* ./AIWebQuickDeploy/public/admin/* ./AIWebQuickDeploy/public/chat/* 2>/dev/null || true
mkdir -p ./AIWebQuickDeploy/dist ./AIWebQuickDeploy/public/admin ./AIWebQuickDeploy/public/chat ./AIWebQuickDeploy/logs

# 复制配置文件
echo "📄 复制配置文件..."
cp service/pm2.conf.json ./AIWebQuickDeploy/pm2.conf.json 2>/dev/null || echo "⚠️  警告: pm2.conf.json 不存在"
cp service/package.json ./AIWebQuickDeploy/package.json

# 复制环境配置文件
cp service/.env.example ./AIWebQuickDeploy/.env.example 2>/dev/null || echo "⚠️  警告: .env.example 不存在"
cp service/.env.docker ./AIWebQuickDeploy/.env.docker 2>/dev/null || echo "⚠️  警告: .env.docker 不存在"
cp service/Dockerfile ./AIWebQuickDeploy/Dockerfile 2>/dev/null || echo "⚠️  警告: Dockerfile 不存在"
cp service/docker-compose.yml ./AIWebQuickDeploy/docker-compose.yml 2>/dev/null || echo "⚠️  警告: docker-compose.yml 不存在"
cp service/.dockerignore ./AIWebQuickDeploy/.dockerignore 2>/dev/null || echo "⚠️  警告: .dockerignore 不存在"

# 复制构建产物
echo "📦 复制构建产物..."
if [ ! -d "service/dist" ]; then
    echo "❌ 错误: service/dist 目录不存在"
    exit 1
fi
cp -a service/dist/* ./AIWebQuickDeploy/dist/

if [ ! -d "admin/dist" ]; then
    echo "❌ 错误: admin/dist 目录不存在"
    exit 1
fi
cp -r admin/dist/* ./AIWebQuickDeploy/public/admin/

if [ ! -d "chat/dist" ]; then
    echo "❌ 错误: chat/dist 目录不存在"
    exit 1
fi
cp -r chat/dist/* ./AIWebQuickDeploy/public/chat/

# 验证构建结果
echo "🔍 验证构建结果..."
if [ ! -f "./AIWebQuickDeploy/dist/main.js" ]; then
    echo "❌ 错误: 后端主文件不存在"
    exit 1
fi

if [ ! -f "./AIWebQuickDeploy/public/admin/index.html" ]; then
    echo "❌ 错误: 管理后台文件不存在"
    exit 1
fi

if [ ! -f "./AIWebQuickDeploy/public/chat/index.html" ]; then
    echo "❌ 错误: 用户界面文件不存在"
    exit 1
fi

echo "======================================="
echo "✅ 打包完成！"
echo "部署目录: ./AIWebQuickDeploy"
echo "======================================="