# 99AI 项目结构图

## 🏗️ 整体架构图

```mermaid
graph TB
    subgraph "前端层"
        A[用户聊天界面<br/>chat - Vue.js 3]
        B[管理后台<br/>admin - Vue.js 3]
        C[移动端应用<br/>Electron跨平台]
    end
    
    subgraph "API网关层"
        D[NestJS后端服务<br/>service - Port 9520]
    end
    
    subgraph "数据存储层"
        E[MySQL数据库<br/>用户/订单/聊天记录]
        F[Redis缓存<br/>会话/配置/临时数据]
        G[文件存储<br/>OSS/本地存储]
    end
    
    subgraph "外部服务"
        H[AI模型服务<br/>OpenAI/Claude/Gemini]
        I[支付服务<br/>微信/支付宝/易支付]
        J[邮件服务<br/>SMTP]
        K[搜索服务<br/>网络搜索API]
    end
    
    A --> D
    B --> D
    C --> D
    D --> E
    D --> F
    D --> G
    D --> H
    D --> I
    D --> J
    D --> K
```

## 📁 详细目录结构

```
99AI-main/
├── 📁 admin/                          # 管理后台前端
│   ├── 📁 src/
│   │   ├── 📁 api/                    # API接口定义
│   │   │   ├── 📄 index.ts            # 统一导出
│   │   │   └── 📁 modules/            # 模块化API
│   │   │       ├── 📄 user.ts         # 用户相关API
│   │   │       ├── 📄 chat.ts         # 聊天相关API
│   │   │       ├── 📄 model.ts        # 模型管理API
│   │   │       └── 📄 order.ts        # 订单相关API
│   │   ├── 📁 components/             # 公共组件
│   │   │   ├── 📁 Auth/               # 权限组件
│   │   │   ├── 📁 FileUpload/         # 文件上传组件
│   │   │   ├── 📁 ImageUpload/        # 图片上传组件
│   │   │   └── 📁 PageHeader/         # 页面头部组件
│   │   ├── 📁 views/                  # 页面视图
│   │   │   ├── 📄 login.vue           # 登录页面
│   │   │   ├── 📁 users/              # 用户管理
│   │   │   ├── 📁 chat/               # 聊天管理
│   │   │   ├── 📁 models/             # 模型管理
│   │   │   ├── 📁 order/              # 订单管理
│   │   │   ├── 📁 pay/                # 支付配置
│   │   │   └── 📁 system/             # 系统设置
│   │   ├── 📁 store/                  # 状态管理
│   │   │   ├── 📄 index.ts            # Store入口
│   │   │   └── 📁 modules/            # 模块化Store
│   │   ├── 📁 router/                 # 路由配置
│   │   │   ├── 📄 index.ts            # 路由入口
│   │   │   └── 📁 modules/            # 路由模块
│   │   └── 📁 utils/                  # 工具函数
│   ├── 📄 package.json                # 依赖配置
│   ├── 📄 vite.config.ts              # Vite构建配置
│   └── 📄 uno.config.ts               # UnoCSS配置
│
├── 📁 chat/                           # 用户聊天前端
│   ├── 📁 src/
│   │   ├── 📁 api/                    # API接口
│   │   │   ├── 📄 index.ts            # 聊天API
│   │   │   ├── 📄 user.ts             # 用户API
│   │   │   ├── 📄 config.ts           # 配置API
│   │   │   └── 📄 upload.ts           # 上传API
│   │   ├── 📁 components/             # 聊天组件
│   │   │   ├── 📁 Message/            # 消息组件
│   │   │   ├── 📁 Settings/           # 设置组件
│   │   │   ├── 📁 Login/              # 登录组件
│   │   │   └── 📁 common/             # 公共组件
│   │   ├── 📁 views/                  # 页面视图
│   │   │   └── 📁 chat/               # 聊天页面
│   │   │       ├── 📄 chat.vue        # 主聊天页面
│   │   │       ├── 📄 chatBase.vue    # 聊天基础组件
│   │   │       ├── 📁 components/     # 聊天子组件
│   │   │       └── 📁 hooks/          # 聊天相关Hooks
│   │   ├── 📁 store/                  # 状态管理
│   │   │   ├── 📄 index.ts            # Store入口
│   │   │   └── 📁 modules/            # 状态模块
│   │   ├── 📁 utils/                  # 工具函数
│   │   │   ├── 📁 request/            # 请求工具
│   │   │   ├── 📁 storage/            # 存储工具
│   │   │   └── 📁 format/             # 格式化工具
│   │   └── 📁 styles/                 # 样式文件
│   │       ├── 📄 index.css           # 主样式
│   │       ├── 📄 tailwind.css        # Tailwind样式
│   │       └── 📁 themes/             # 主题样式
│   ├── 📄 package.json                # 依赖配置
│   ├── 📄 vite.config.ts              # Vite构建配置
│   └── 📄 tailwind.config.js          # Tailwind配置
│
├── 📁 service/                        # 后端服务
│   ├── 📁 src/
│   │   ├── 📄 main.ts                 # 应用入口
│   │   ├── 📄 app.module.ts           # 根模块
│   │   ├── 📁 modules/                # 业务模块
│   │   │   ├── 📁 auth/               # 认证模块
│   │   │   │   ├── 📄 auth.module.ts
│   │   │   │   ├── 📄 auth.service.ts
│   │   │   │   ├── 📄 auth.controller.ts
│   │   │   │   └── 📁 dto/            # 数据传输对象
│   │   │   ├── 📁 user/               # 用户模块
│   │   │   │   ├── 📄 user.module.ts
│   │   │   │   ├── 📄 user.service.ts
│   │   │   │   ├── 📄 user.controller.ts
│   │   │   │   └── 📁 entities/       # 实体定义
│   │   │   ├── 📁 chat/               # 聊天模块
│   │   │   │   ├── 📄 chat.module.ts
│   │   │   │   ├── 📄 chat.service.ts
│   │   │   │   └── 📄 chat.controller.ts
│   │   │   ├── 📁 models/             # 模型管理
│   │   │   ├── 📁 pay/                # 支付模块
│   │   │   ├── 📁 order/              # 订单模块
│   │   │   ├── 📁 upload/             # 文件上传
│   │   │   ├── 📁 plugin/             # 插件系统
│   │   │   └── 📁 aiTool/             # AI工具
│   │   │       ├── 📁 chat/           # AI聊天服务
│   │   │       ├── 📁 search/         # 网络搜索
│   │   │       └── 📁 adapters/       # AI模型适配器
│   │   ├── 📁 common/                 # 公共模块
│   │   │   ├── 📁 guards/             # 守卫
│   │   │   │   ├── 📄 jwt-auth.guard.ts
│   │   │   │   ├── 📄 admin-auth.guard.ts
│   │   │   │   └── 📄 super-auth.guard.ts
│   │   │   ├── 📁 filters/            # 异常过滤器
│   │   │   ├── 📁 interceptors/       # 拦截器
│   │   │   ├── 📁 decorators/         # 装饰器
│   │   │   ├── 📁 middleware/         # 中间件
│   │   │   ├── 📁 utils/              # 工具类
│   │   │   └── 📁 entity/             # 基础实体
│   │   └── 📁 types/                  # 类型定义
│   ├── 📄 package.json                # 依赖配置
│   ├── 📄 nest-cli.json               # NestJS CLI配置
│   ├── 📄 tsconfig.json               # TypeScript配置
│   └── 📄 .env.example                # 环境变量示例
│
├── 📁 AIWebQuickDeploy/               # 部署包
│   ├── 📁 dist/                       # 编译后的后端代码
│   ├── 📁 public/                     # 静态资源
│   │   ├── 📁 admin/                  # 管理后台静态文件
│   │   ├── 📁 chat/                   # 聊天界面静态文件
│   │   └── 📁 file/                   # 上传文件存储
│   ├── 📄 docker-compose.yml          # Docker编排文件
│   ├── 📄 Dockerfile                  # Docker镜像构建
│   ├── 📄 package.json                # 生产环境依赖
│   ├── 📄 pm2.conf.json               # PM2进程配置
│   ├── 📄 .env.example                # 环境变量示例
│   └── 📄 .env.docker                 # Docker环境变量
│
├── 📁 docs/                           # 项目文档
│   ├── 📄 DEPLOYMENT.md               # 部署文档
│   ├── 📄 DEVELOPMENT.md              # 开发文档
│   └── 📄 FEATURES.md                 # 功能介绍
│
├── 📄 build.sh                        # 构建脚本
├── 📄 README.md                       # 项目说明
└── 📄 LICENSE                         # 开源协议
```

## 🔄 数据流向图

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端界面
    participant A as API网关
    participant S as 业务服务
    participant D as 数据库
    participant R as Redis
    participant AI as AI服务
    
    U->>F: 发送消息
    F->>A: POST /api/chatgpt/chat-process
    A->>S: 验证用户权限
    S->>D: 查询用户信息
    S->>R: 检查缓存
    S->>AI: 调用AI模型
    AI-->>S: 返回AI响应
    S->>D: 保存聊天记录
    S->>R: 更新缓存
    S-->>A: 流式返回结果
    A-->>F: 实时推送消息
    F-->>U: 显示AI回复
```

## 🗄️ 数据库关系图

```mermaid
erDiagram
    users ||--o{ chat_log : "用户聊天"
    users ||--o{ chat_group : "聊天分组"
    users ||--|| user_balance : "用户余额"
    users ||--o{ order : "用户订单"
    users ||--o{ account_log : "账户日志"
    
    chat_group ||--o{ chat_log : "分组聊天"
    app ||--o{ chat_log : "应用聊天"
    app ||--o{ chat_group : "应用分组"
    app_cats ||--o{ app : "应用分类"
    
    crami_package ||--o{ order : "套餐订单"
    models ||--o{ chat_log : "模型使用"
    
    users {
        int id PK
        string username
        string password
        string email
        string phone
        string avatar
        string role
        int status
        datetime createdAt
    }
    
    chat_log {
        int id PK
        int userId FK
        int groupId FK
        int appId FK
        text prompt
        longtext content
        string model
        int tokens
        datetime createdAt
    }
    
    user_balance {
        int id PK
        int userId FK
        decimal balance
        bigint usedTokens
        int paintCount
    }
    
    order {
        int id PK
        int userId FK
        string orderId
        int goodsId FK
        decimal price
        string payType
        int status
        datetime payTime
    }
```

## 🔧 核心模块关系图

```mermaid
graph LR
    subgraph "认证授权"
        A1[AuthModule]
        A2[JwtAuthGuard]
        A3[AdminAuthGuard]
        A4[SuperAuthGuard]
    end
    
    subgraph "用户管理"
        B1[UserModule]
        B2[UserBalanceModule]
        B3[VerificationModule]
    end
    
    subgraph "聊天系统"
        C1[ChatModule]
        C2[ChatLogModule]
        C3[ChatGroupModule]
        C4[ModelsModule]
    end
    
    subgraph "AI服务"
        D1[OpenAIChatService]
        D2[NetSearchService]
        D3[PluginModule]
    end
    
    subgraph "商业系统"
        E1[PayModule]
        E2[OrderModule]
        E3[CramiModule]
    end
    
    subgraph "系统服务"
        F1[UploadModule]
        F2[MailerModule]
        F3[GlobalConfigModule]
        F4[RedisCacheModule]
    end
    
    A1 --> B1
    B1 --> C1
    C1 --> D1
    C1 --> D2
    B2 --> E1
    E1 --> E2
    C1 --> F1
    B1 --> F2
    C1 --> F4
```

## 📱 前端组件层次图

```mermaid
graph TD
    subgraph "聊天界面 (chat)"
        A[App.vue]
        A --> B[ChatBase.vue]
        B --> C[Header组件]
        B --> D[Sider组件]
        B --> E[Message组件]
        B --> F[Footer组件]
        
        C --> C1[用户信息]
        C --> C2[设置按钮]
        
        D --> D1[对话列表]
        D --> D2[应用广场]
        
        E --> E1[消息列表]
        E --> E2[输入框]
        E --> E3[工具栏]
        
        F --> F1[模型选择]
        F --> F2[功能开关]
    end
    
    subgraph "管理后台 (admin)"
        G[App.vue]
        G --> H[Layout布局]
        H --> I[侧边栏]
        H --> J[顶部栏]
        H --> K[主内容区]
        
        I --> I1[菜单导航]
        J --> J1[用户信息]
        J --> J2[系统通知]
        K --> K1[数据表格]
        K --> K2[表单组件]
        K --> K3[图表组件]
    end
```

这个项目结构图详细展示了99AI项目的整体架构、目录结构、数据流向、数据库关系以及核心模块之间的关系，帮助您更好地理解和修改这个项目。
