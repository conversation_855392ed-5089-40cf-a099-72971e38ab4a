# 99AI 数据库连接问题解决方案

## 🔍 问题描述
应用启动时出现MySQL连接错误：
```
Error: Access denied for user 'chat'@'localhost' (using password: YES)
```

## 🛠️ 解决方案

### 方案一：修改环境配置文件（推荐）

1. **检查当前配置**
   在 `AIWebQuickDeploy` 目录下查看 `.env` 文件：
   ```bash
   cd /www/wwwroot/333/99AI-main/AIWebQuickDeploy
   cat .env
   ```

2. **修改数据库配置**
   编辑 `.env` 文件，修改数据库连接信息：
   ```bash
   # MySQL配置
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_USER=root              # 改为您的MySQL用户名
   DB_PASS=your_password     # 改为您的MySQL密码
   DB_DATABASE=99ai          # 改为您的数据库名
   ```

3. **重启服务**
   ```bash
   pm2 restart 99AI
   ```

### 方案二：创建专用数据库用户

如果您想继续使用 `chat` 用户，需要在MySQL中创建该用户：

1. **登录MySQL**
   ```bash
   mysql -u root -p
   ```

2. **创建数据库和用户**
   ```sql
   -- 创建数据库
   CREATE DATABASE IF NOT EXISTS 99ai CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   
   -- 创建用户
   CREATE USER 'chat'@'localhost' IDENTIFIED BY 'your_password';
   
   -- 授权
   GRANT ALL PRIVILEGES ON 99ai.* TO 'chat'@'localhost';
   
   -- 刷新权限
   FLUSH PRIVILEGES;
   
   -- 退出
   EXIT;
   ```

3. **更新环境配置**
   ```bash
   # 在 .env 文件中设置
   DB_USER=chat
   DB_PASS=your_password
   DB_DATABASE=99ai
   ```

### 方案三：使用现有数据库

如果您已经有数据库，只需修改配置指向正确的数据库：

1. **查看现有数据库**
   ```sql
   SHOW DATABASES;
   ```

2. **修改配置文件**
   ```bash
   # 在 .env 文件中设置正确的数据库信息
   DB_USER=existing_user
   DB_PASS=existing_password
   DB_DATABASE=existing_database
   ```

## 📝 配置文件示例

创建或修改 `AIWebQuickDeploy/.env` 文件：

```bash
# 端口
PORT=9520

# MySQL配置
DB_HOST=127.0.0.1
DB_PORT=3306
DB_USER=root                    # 您的MySQL用户名
DB_PASS=your_mysql_password     # 您的MySQL密码
DB_DATABASE=99ai                # 数据库名

# Redis配置
REDIS_PORT=6379
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=
REDIS_USER=
REDIS_DB=0

# 是否测试环境
ISDEV=false

# 自定义后台路径
ADMIN_SERVE_ROOT=/admin

# 其他配置...
```

## 🔧 常用命令

### 检查MySQL服务状态
```bash
systemctl status mysql
# 或
service mysql status
```

### 重启MySQL服务
```bash
systemctl restart mysql
# 或
service mysql restart
```

### 检查MySQL用户
```sql
SELECT User, Host FROM mysql.user;
```

### 查看用户权限
```sql
SHOW GRANTS FOR 'chat'@'localhost';
```

### 重启99AI服务
```bash
pm2 restart 99AI
pm2 logs 99AI
```

## ⚠️ 注意事项

1. **密码安全**：确保数据库密码足够强壮
2. **权限最小化**：只授予应用需要的最小权限
3. **备份数据**：修改前请备份重要数据
4. **防火墙**：确保MySQL端口3306的安全配置

## 🚀 验证解决方案

修改配置后，检查应用日志：
```bash
pm2 logs 99AI
```

如果看到类似以下信息，说明连接成功：
```
[TypeOrmModule] Database connection successful
[NestApplication] Nest application successfully started
```

## 📞 需要帮助？

如果问题仍然存在，请提供：
1. 当前的 `.env` 文件内容（隐藏敏感信息）
2. MySQL版本信息
3. 完整的错误日志

这样我可以提供更具体的解决方案。
