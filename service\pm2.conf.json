{"apps": [{"name": "JianfaAI", "script": "./dist/main.js", "cwd": "./", "watch": false, "ignore_watch": ["node_modules", "logs", "public/file", "*.log"], "env": {"TZ": "Asia/Shanghai", "NODE_ENV": "production"}, "instances": 1, "error_file": "./logs/err.log", "out_file": "./logs/out.log", "log_file": "./logs/combined.log", "log_date_format": "YYYY-MM-DD HH:mm:ss", "max_memory_restart": "2000M", "restart_delay": 3000, "max_restarts": 5, "min_uptime": "30s", "autorestart": true, "kill_timeout": 5000, "listen_timeout": 10000, "exec_mode": "fork", "merge_logs": true, "time": true}]}