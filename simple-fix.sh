#!/bin/bash

# 简化修复脚本 - 直接解决lockfile问题

echo "======================================="
echo "        简化修复脚本"
echo "======================================="

# 检查pnpm
command -v pnpm >/dev/null 2>&1 || { echo "❌ 错误: 需要安装 pnpm"; exit 1; }

echo "🔧 开始简化修复..."

# 1. 直接删除所有lockfile
echo "🧹 删除所有lockfile..."
find . -name "pnpm-lock.yaml" -delete
echo "✅ 已删除所有pnpm-lock.yaml文件"

# 2. 修复service目录
echo "📦 修复后端服务..."
cd service/

echo "📦 安装后端依赖..."
if pnpm install; then
    echo "✅ 后端依赖安装成功"
else
    echo "❌ 后端依赖安装失败"
    exit 1
fi

echo "🔨 构建后端服务..."
if pnpm build; then
    echo "✅ 后端构建成功"
else
    echo "❌ 后端构建失败"
    exit 1
fi

cd ..

# 3. 修复admin目录
echo "📦 修复管理后台..."
cd admin/

echo "📦 安装管理后台依赖..."
if pnpm install; then
    echo "✅ 管理后台依赖安装成功"
else
    echo "❌ 管理后台依赖安装失败"
    exit 1
fi

echo "🔨 构建管理后台..."
if pnpm build; then
    echo "✅ 管理后台构建成功"
else
    echo "❌ 管理后台构建失败"
    exit 1
fi

cd ..

# 4. 修复chat目录
echo "📦 修复用户界面..."
cd chat/

echo "📦 安装用户界面依赖..."
if pnpm install; then
    echo "✅ 用户界面依赖安装成功"
else
    echo "❌ 用户界面依赖安装失败"
    exit 1
fi

echo "🔨 构建用户界面..."
if pnpm build; then
    echo "✅ 用户界面构建成功"
else
    echo "❌ 用户界面构建失败"
    exit 1
fi

cd ..

echo ""
echo "======================================="
echo "✅ 简化修复完成！"
echo ""
echo "所有项目构建成功，现在可以运行："
echo "  ./build.sh          # 完整构建和打包"
echo "======================================="
