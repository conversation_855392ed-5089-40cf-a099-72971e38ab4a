# actualOpenaiBaseUrl 参数说明与修改记录

## 📋 参数作用说明

### 🔍 `actualOpenaiBaseUrl` 的作用

`actualOpenaiBaseUrl` 是一个 Vue 计算属性，位于 `admin/src/views/models/baseSetting.vue` 文件中，主要作用包括：

1. **URL 自动规范化**
   - 自动去除URL末尾的斜杠 `/`
   - 检查URL是否包含版本号（如 `/v1`, `/v1beta` 等）
   - 如果没有版本号，自动添加 `/v1`

2. **实时预览功能**
   - 在用户输入API地址后，实时显示系统实际调用的地址
   - 帮助用户确认配置是否正确
   - 避免因URL格式问题导致的调用失败

3. **用户体验优化**
   - 用户只需输入基础地址，系统自动处理技术细节
   - 减少配置错误的可能性

### 🛠️ 工作原理示例

| 用户输入 | 系统处理后 | 说明 |
|---------|-----------|------|
| `https://api.openai.com` | `https://api.openai.com/v1` | 自动添加 /v1 |
| `https://api.openai.com/` | `https://api.openai.com/v1` | 去除末尾斜杠，添加 /v1 |
| `https://api.openai.com/v1` | `https://api.openai.com/v1` | 已有版本号，保持不变 |
| `https://api.openai.com/v1beta` | `https://api.openai.com/v1beta` | 已有版本号，保持不变 |

### 📍 代码位置

**文件路径**: `admin/src/views/models/baseSetting.vue`

**相关代码**:
```javascript
// 第 268-286 行：URL规范化函数
const correctApiBaseUrl = (baseUrl: string): string => {
  if (!baseUrl) return '';
  
  let url = baseUrl.trim();
  
  if (url.endsWith('/')) {
    url = url.slice(0, -1);
  }
  
  if (!/\/v\d+(?:beta|alpha)?/.test(url)) {
    return `${url}/v1`;
  }
  
  return url;
};

// 第 289 行：计算属性定义
const actualOpenaiBaseUrl = computed(() => correctApiBaseUrl(formInline.openaiBaseUrl));
```

**模板使用**:
```vue
<!-- 第 347-349 行：显示实际调用地址 -->
<div v-if="actualOpenaiBaseUrl" class="text-xs text-gray-400 mt-1">
  最终调用地址：{{ actualOpenaiBaseUrl }}
</div>
```

## 🎯 已完成的修改

### 1. 品牌信息更新
**修改位置**: 第 314-319 行
**修改内容**:
```vue
<!-- 修改前 -->
<div>
  一站式法律Ai服务接口
  <a href="https://jianfaai.com" target="_blank" style="margin-right: 5px">
    https://jianfaai.com
  </a>
  ，提供 法律 AI 接口服务，无强制绑定关系。
</div>

<!-- 修改后 -->
<div>
  JianfaAI 智能服务平台
  <a href="https://jianfaai.com" target="_blank" style="margin-right: 5px">
    https://jianfaai.com
  </a>
  ，提供专业的 AI 接口服务，支持多种模型调用。
</div>
```

### 2. 选项标签优化
**修改位置**: 第 54-57 行
**修改内容**:
```javascript
// 修改前
{
  value: 'https://api.jianfaai.com',
  label: '【JianfaAI API】https://api.jianfaai.com',
}

// 修改后
{
  value: 'https://api.jianfaai.com',
  label: '【JianfaAI 推荐】https://api.jianfaai.com',
}
```

### 3. 显示文本统一
**修改位置**: 第 347-349 行 和 第 396-398 行
**修改内容**:
```vue
<!-- 修改前 -->
实际调用地址：{{ actualOpenaiBaseUrl }}

<!-- 修改后 -->
最终调用地址：{{ actualOpenaiBaseUrl }}
```

## 🔧 其他相关计算属性

该文件中还定义了其他类似的计算属性：

1. `actualDeepThinkingUrl` - 深度思考模型地址
2. `actualVectorUrl` - 向量模型地址  
3. `actualToolCallUrl` - 工具调用模型地址
4. `actualImageAnalysisUrl` - 图片解析模型地址
5. `actualPluginUrl` - 插件地址

这些都使用相同的 `correctApiBaseUrl` 函数进行URL规范化处理。

## 📝 配置说明

### 全局配置的作用
- **全局地址**: 当单个模型没有配置专用地址时，使用此地址
- **全局 Key**: 当单个模型没有配置专用密钥时，使用此密钥
- **全局模型**: 用于后台静默操作，如生成对话标题、提问建议等

### 预设选项
系统预设了几个常用的API服务商：
- DeepSeek 官方
- 阿里云百炼
- 腾讯云知识引擎
- JianfaAI 推荐
- 其他（自定义）

## 🚀 使用建议

1. **选择合适的服务商**: 根据需求选择预设选项或自定义
2. **确认最终地址**: 输入后查看"最终调用地址"确保正确
3. **测试连接**: 保存配置后建议测试API连接是否正常
4. **备用配置**: 可以配置多个不同的模型地址作为备用

## ⚠️ 注意事项

1. **版本兼容性**: 不同API服务商可能使用不同的版本路径
2. **网络访问**: 确保服务器能够访问配置的API地址
3. **密钥安全**: 妥善保管API密钥，避免泄露
4. **配额限制**: 注意各服务商的调用频率和配额限制

所有修改已完成，界面将显示更符合 JianfaAI 品牌的内容。
