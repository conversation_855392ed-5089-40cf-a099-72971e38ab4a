#!/bin/bash

# 紧急修复脚本 - 解决PM2启动问题

echo "======================================="
echo "        紧急修复PM2启动问题"
echo "======================================="

# 1. 停止所有PM2进程
echo "🛑 停止所有PM2进程..."
pm2 stop all 2>/dev/null || true
pm2 delete all 2>/dev/null || true
pm2 kill 2>/dev/null || true

# 2. 创建必要的目录和文件
echo "📁 创建必要的目录和文件..."
mkdir -p logs
mkdir -p public/file
mkdir -p public/uploads

# 创建日志文件
touch logs/out.log
touch logs/err.log
touch logs/combined.log

# 3. 检查关键文件
echo "🔍 检查关键文件..."
if [ ! -f "dist/main.js" ]; then
    echo "❌ dist/main.js 不存在"
    exit 1
fi

if [ ! -f ".env" ]; then
    echo "📄 创建.env文件..."
    if [ -f ".env.example" ]; then
        cp .env.example .env
    else
        cat > .env << 'EOF'
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASS=
DB_DATABASE=jianfaai

# Redis配置（可选）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 服务配置
PORT=9520
ADMIN_SERVE_ROOT=/admin

# 开发模式
ISDEV=false
EOF
    fi
    echo "⚠️  请编辑.env文件配置正确的数据库信息"
fi

# 4. 修复PM2配置
echo "🔧 修复PM2配置..."
cat > pm2.conf.json << 'EOF'
{
  "apps": [
    {
      "name": "JianfaAI",
      "script": "./dist/main.js",
      "cwd": "./",
      "watch": false,
      "env": {
        "TZ": "Asia/Shanghai",
        "NODE_ENV": "production"
      },
      "instances": 1,
      "exec_mode": "fork",
      "error_file": "./logs/err.log",
      "out_file": "./logs/out.log",
      "log_file": "./logs/combined.log",
      "log_date_format": "YYYY-MM-DD HH:mm:ss",
      "max_memory_restart": "2000M",
      "restart_delay": 5000,
      "max_restarts": 3,
      "min_uptime": "10s",
      "autorestart": true,
      "kill_timeout": 5000,
      "merge_logs": true,
      "time": true
    }
  ]
}
EOF

echo "✅ PM2配置已修复"

# 5. 测试应用启动
echo "🧪 测试应用启动..."
echo "正在测试应用是否能正常启动..."

# 直接运行应用查看错误
timeout 15s node dist/main.js 2>&1 | tee startup-test.log &
TEST_PID=$!

sleep 10

if kill -0 $TEST_PID 2>/dev/null; then
    echo "✅ 应用正在运行"
    kill $TEST_PID 2>/dev/null || true
    wait $TEST_PID 2>/dev/null || true
else
    echo "❌ 应用启动失败，查看错误日志："
    cat startup-test.log
    echo ""
    echo "常见问题："
    echo "1. 数据库连接失败 - 请检查.env中的数据库配置"
    echo "2. 端口被占用 - 请检查端口9520是否被占用"
    echo "3. 权限问题 - 请检查文件权限"
    exit 1
fi

# 6. 启动PM2服务
echo "🚀 启动PM2服务..."
pm2 start pm2.conf.json

# 7. 等待服务稳定
echo "⏳ 等待服务稳定..."
sleep 8

# 8. 检查服务状态
echo "📊 检查服务状态..."
pm2 status

# 9. 检查应用是否真正在线
if pm2 list | grep -q "online"; then
    echo "✅ 服务启动成功！"
    
    # 显示访问信息
    echo ""
    echo "🌐 访问地址："
    echo "  前端用户界面: http://localhost:9520"
    echo "  管理后台: http://localhost:9520/admin"
    echo ""
    echo "📝 默认管理员账号："
    echo "  用户名: super"
    echo "  密码: 123456"
    
else
    echo "❌ 服务启动失败，查看详细日志："
    pm2 logs JianfaAI --lines 30
    
    echo ""
    echo "🔧 故障排查："
    echo "1. 检查数据库连接："
    echo "   mysql -h数据库地址 -u用户名 -p数据库名"
    echo ""
    echo "2. 检查端口占用："
    echo "   netstat -tlnp | grep 9520"
    echo ""
    echo "3. 手动启动查看错误："
    echo "   node dist/main.js"
    echo ""
    echo "4. 查看详细日志："
    echo "   pm2 logs JianfaAI"
fi

# 清理临时文件
rm -f startup-test.log

echo ""
echo "======================================="
echo "紧急修复完成"
echo "======================================="
