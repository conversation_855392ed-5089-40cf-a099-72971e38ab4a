#!/bin/bash

# 修复启动问题的脚本

echo "======================================="
echo "        修复启动问题"
echo "======================================="

# 1. 停止现有的PM2进程
echo "🛑 停止现有PM2进程..."
pm2 stop all 2>/dev/null || true
pm2 delete all 2>/dev/null || true

# 2. 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p logs
mkdir -p public/file
mkdir -p public/uploads
mkdir -p public/admin
mkdir -p public/chat

echo "✅ 目录创建完成"

# 3. 检查关键文件
echo "🔍 检查关键文件..."

if [ ! -f "dist/main.js" ]; then
    echo "❌ dist/main.js 不存在"
    echo "请先运行构建: ./build.sh"
    exit 1
else
    echo "✅ dist/main.js 存在"
fi

if [ ! -f ".env" ]; then
    echo "⚠️  .env 文件不存在"
    if [ -f ".env.example" ]; then
        echo "📄 复制 .env.example 到 .env"
        cp .env.example .env
        echo "⚠️  请编辑 .env 文件配置数据库信息"
    else
        echo "❌ .env.example 也不存在，请手动创建 .env 文件"
        exit 1
    fi
else
    echo "✅ .env 文件存在"
fi

# 4. 检查PM2配置
echo "🔍 检查PM2配置..."
if [ ! -f "pm2.conf.json" ]; then
    echo "❌ pm2.conf.json 不存在"
    exit 1
else
    echo "✅ pm2.conf.json 存在"
fi

# 5. 测试应用启动
echo "🧪 测试应用启动..."
echo "正在测试应用是否能正常启动（10秒超时）..."

timeout 10s node dist/main.js &
TEST_PID=$!

sleep 3

if kill -0 $TEST_PID 2>/dev/null; then
    echo "✅ 应用可以正常启动"
    kill $TEST_PID 2>/dev/null || true
else
    echo "❌ 应用启动失败"
    echo "请检查以下内容："
    echo "1. 数据库配置是否正确"
    echo "2. .env 文件是否配置完整"
    echo "3. 数据库是否可连接"
    exit 1
fi

# 6. 启动PM2
echo "🚀 启动PM2..."
pm2 start pm2.conf.json

# 7. 检查状态
sleep 3
echo "📊 检查PM2状态..."
pm2 status

echo ""
echo "======================================="
echo "✅ 启动修复完成！"
echo ""
echo "如果应用仍然有问题，请运行："
echo "  pm2 logs JianfaAI    # 查看详细日志"
echo "  pm2 restart JianfaAI # 重启应用"
echo "  pm2 stop JianfaAI    # 停止应用"
echo "======================================="
