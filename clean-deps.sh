#!/bin/bash

# 依赖清理脚本
# 清理所有项目的node_modules和lockfile，重新安装依赖

echo "======================================="
echo "        依赖清理和重新安装"
echo "======================================="

# 检查pnpm
command -v pnpm >/dev/null 2>&1 || { echo "❌ 错误: 需要安装 pnpm"; exit 1; }

# 清理函数
clean_project() {
    local project_dir=$1
    local project_name=$2
    
    echo "🧹 清理 ${project_name}..."
    cd "$project_dir"
    
    if [ -f "package.json" ]; then
        # 删除node_modules和lockfile
        rm -rf node_modules
        rm -f pnpm-lock.yaml
        rm -f package-lock.json
        rm -f yarn.lock
        
        echo "✅ ${project_name} 清理完成"
    else
        echo "⚠️  ${project_name} 没有package.json，跳过"
    fi
    
    cd ..
}

# 安装函数
install_project() {
    local project_dir=$1
    local project_name=$2
    
    echo "📦 安装 ${project_name} 依赖..."
    cd "$project_dir"
    
    if [ -f "package.json" ]; then
        # 清理pnpm缓存（可选）
        pnpm store prune 2>/dev/null || true
        
        # 重新安装依赖
        if pnpm install; then
            echo "✅ ${project_name} 依赖安装成功"
        else
            echo "❌ ${project_name} 依赖安装失败"
            exit 1
        fi
    else
        echo "⚠️  ${project_name} 没有package.json，跳过"
    fi
    
    cd ..
}

# 主清理流程
echo "🧹 开始清理所有项目依赖..."

# 清理所有项目
clean_project "admin" "管理后台"
clean_project "chat" "用户界面"
clean_project "service" "后端服务"
clean_project "AIWebQuickDeploy" "部署包"

echo ""
echo "📦 开始重新安装依赖..."

# 重新安装所有项目依赖
install_project "admin" "管理后台"
install_project "chat" "用户界面"
install_project "service" "后端服务"

echo ""
echo "======================================="
echo "✅ 依赖清理和重新安装完成！"
echo "现在可以运行 ./build.sh 进行构建"
echo "======================================="
