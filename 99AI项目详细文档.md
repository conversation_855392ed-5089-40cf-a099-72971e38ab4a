# 99AI 项目详细文档

## 📋 目录
- [项目概述](#项目概述)
- [技术架构](#技术架构)
- [项目结构](#项目结构)
- [核心功能](#核心功能)
- [技术栈详解](#技术栈详解)
- [部署指南](#部署指南)
- [开发指南](#开发指南)
- [API接口](#api接口)
- [数据库设计](#数据库设计)
- [配置说明](#配置说明)

## 🌟 项目概述

99AI 是一个**可商业化的 AI Web 平台**，提供一站式的人工智能服务解决方案。支持私有化部署，内置多用户管理，适合企业、团队或个人快速构建 AI 服务。

### 核心优势
- **开箱即用**：基于 Node.js 完整打包，支持 Docker 部署
- **功能丰富**：集成主流 AI 能力，覆盖多场景应用
- **安全可控**：支持私有化部署，数据自主管理
- **开发友好**：提供源码级访问，支持二次开发与功能扩展
- **商业支持**：内置多种支付方式，支持商业化运营

### 版本信息
- **当前版本**：4.3.0
- **开源协议**：Apache 2.0
- **作者**：vastxie
- **GitHub**：https://github.com/vastxie/99AI

## 🏗️ 技术架构

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端用户界面   │    │   管理后台界面   │    │   移动端应用     │
│   (chat)        │    │   (admin)       │    │   (Electron)    │
│   Vue.js 3      │    │   Vue.js 3      │    │   跨平台客户端   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   后端服务API    │
                    │   (service)     │
                    │   NestJS        │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   MySQL数据库    │    │   Redis缓存     │    │   文件存储       │
│   用户/订单/日志  │    │   会话/配置     │    │   OSS/本地      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 微服务架构
- **前端分离**：用户界面、管理后台、移动端独立部署
- **API网关**：统一的后端服务接口
- **数据层**：MySQL + Redis 双重存储
- **文件服务**：支持多种云存储方案

## 📁 项目结构

```
99AI-main/
├── admin/                    # 管理后台前端
│   ├── src/
│   │   ├── api/             # API接口定义
│   │   ├── components/      # 公共组件
│   │   ├── views/           # 页面视图
│   │   ├── store/           # 状态管理
│   │   ├── router/          # 路由配置
│   │   └── utils/           # 工具函数
│   ├── package.json         # 依赖配置
│   └── vite.config.ts       # 构建配置
│
├── chat/                     # 用户聊天前端
│   ├── src/
│   │   ├── api/             # API接口
│   │   ├── components/      # 聊天组件
│   │   ├── views/           # 聊天界面
│   │   ├── store/           # 状态管理
│   │   ├── utils/           # 工具函数
│   │   └── styles/          # 样式文件
│   ├── package.json
│   └── vite.config.ts
│
├── service/                  # 后端服务
│   ├── src/
│   │   ├── modules/         # 业务模块
│   │   │   ├── auth/        # 认证模块
│   │   │   ├── chat/        # 聊天模块
│   │   │   ├── user/        # 用户模块
│   │   │   ├── models/      # 模型管理
│   │   │   ├── pay/         # 支付模块
│   │   │   ├── order/       # 订单模块
│   │   │   └── ...          # 其他模块
│   │   ├── common/          # 公共模块
│   │   │   ├── guards/      # 守卫
│   │   │   ├── filters/     # 过滤器
│   │   │   ├── interceptors/# 拦截器
│   │   │   └── utils/       # 工具类
│   │   ├── main.ts          # 应用入口
│   │   └── app.module.ts    # 根模块
│   ├── package.json
│   └── nest-cli.json
│
├── AIWebQuickDeploy/         # 部署包
│   ├── dist/                # 编译后的后端代码
│   ├── public/              # 静态资源
│   │   ├── admin/           # 管理后台静态文件
│   │   ├── chat/            # 聊天界面静态文件
│   │   └── file/            # 上传文件存储
│   ├── docker-compose.yml   # Docker编排
│   ├── Dockerfile           # Docker镜像
│   ├── package.json         # 生产依赖
│   └── pm2.conf.json        # PM2配置
│
├── docs/                     # 项目文档
│   ├── DEPLOYMENT.md        # 部署文档
│   ├── DEVELOPMENT.md       # 开发文档
│   └── FEATURES.md          # 功能介绍
│
├── build.sh                 # 构建脚本
├── README.md                # 项目说明
└── LICENSE                  # 开源协议
```

## 🚀 核心功能

### 1. AI 对话系统
- **多模型支持**：OpenAI GPT、Claude、Gemini等主流模型
- **流式响应**：实时打字效果，提升用户体验
- **上下文管理**：智能对话历史管理
- **多模态输入**：支持文本、图片、文件上传
- **深度思考**：支持深度思考模型增强普通模型能力

### 2. 智能功能扩展
- **联网搜索**：实时获取最新信息
- **智能图表**：自动生成Mermaid流程图、思维导图
- **代码预览**：HTML代码实时预览和编辑
- **文件分析**：支持PDF、Word、Excel等文件解析
- **语音合成**：TTS文字转语音功能

### 3. 创意内容生成
- **AI绘画**：集成Midjourney、DALL-E、Stable Diffusion
- **AI音乐**：对接Suno音乐创作
- **AI视频**：支持Luma视频生成

### 4. 应用生态
- **应用广场**：自定义AI智能体和预设
- **知识库**：文档上传和智能问答
- **插件系统**：可扩展的功能插件架构

### 5. 用户管理系统
- **多级权限**：超级管理员、普通管理员、用户
- **积分系统**：灵活的计费和额度管理
- **实名认证**：支持身份验证
- **登录方式**：邮箱、手机、微信多种登录

### 6. 商业化功能
- **支付集成**：微信支付、支付宝、易支付等
- **套餐管理**：灵活的会员套餐配置
- **订单系统**：完整的订单流程管理
- **数据统计**：用户行为和收入分析

## 💻 技术栈详解

### 前端技术栈

#### 用户聊天界面 (chat)
- **框架**：Vue.js 3.5.13
- **构建工具**：Vite 4.5.14
- **状态管理**：Pinia 2.3.1
- **路由**：Vue Router 4.5.0
- **UI组件**：
  - Tailwind CSS 3.4.17 (样式框架)
  - Element Plus (部分组件)
  - @icon-park/vue-next (图标)
- **编辑器**：
  - CodeMirror 6 (代码编辑)
  - md-editor-v3 (Markdown编辑)
- **图表可视化**：
  - Mermaid 11.6.0 (流程图)
  - Markmap (思维导图)
- **文件处理**：
  - mammoth (Word文档)
  - pdfjs-dist (PDF解析)
  - xlsx (Excel处理)
- **其他功能**：
  - html2pdf.js (PDF导出)
  - qrcode (二维码生成)
  - katex (数学公式渲染)

#### 管理后台 (admin)
- **框架**：Vue.js 3.5.13
- **构建工具**：Vite 6.3.5
- **状态管理**：Pinia 2.1.7
- **UI框架**：Element Plus 2.9.10
- **样式**：
  - UnoCSS 66.1.1 (原子化CSS)
  - Less 4.3.0
- **图表**：ECharts 5.6.0
- **编辑器**：md-editor-v3 5.5.1
- **工具库**：
  - lodash-es 4.17.21
  - dayjs 1.11.13
  - axios 1.9.0

### 后端技术栈 (service)

#### 核心框架
- **框架**：NestJS 10.4.17
- **语言**：TypeScript 5.8.3
- **运行时**：Node.js 18+

#### 数据库
- **关系型数据库**：MySQL 8.0
- **ORM**：TypeORM 0.3.22
- **缓存数据库**：Redis (ioredis 5.6.1)

#### AI集成
- **OpenAI**：openai 4.96.0
- **Google AI**：@google/genai 0.10.0
- **LangChain**：
  - @langchain/core 0.3.55
  - @langchain/openai 0.5.10
  - @langchain/community 0.3.42
  - @langchain/langgraph 0.2.72

#### 认证与安全
- **JWT**：@nestjs/jwt 10.2.0
- **Passport**：@nestjs/passport 10.0.3
- **加密**：bcryptjs 2.4.3

#### 文件处理
- **文档解析**：
  - mammoth 1.9.0 (Word)
  - pdf-parse 1.1.1 (PDF)
  - xlsx (Excel)
  - pptxtojson 1.3.1 (PowerPoint)
- **云存储**：
  - @aws-sdk/client-s3 (AWS S3)
  - ali-oss 6.22.0 (阿里云OSS)
  - cos-nodejs-sdk-v5 (腾讯云COS)

#### 支付集成
- **微信支付**：wechatpay-node-v3 2.2.1
- **其他支付**：支持易支付、虎皮椒等多种支付方式

#### 工具库
- **HTTP客户端**：axios 1.8.4
- **时间处理**：dayjs 1.11.13
- **邮件服务**：nodemailer 6.10.1
- **任务调度**：node-cron 3.0.3
- **进程管理**：pm2 6.0.5

### 部署技术栈
- **容器化**：Docker + Docker Compose
- **进程管理**：PM2
- **反向代理**：Nginx (推荐)
- **包管理**：pnpm

## 🔧 核心业务模块详解

### 1. 聊天模块 (Chat Module)
**位置**：`service/src/modules/chat/`

**主要功能**：
- 处理用户与AI的对话请求
- 管理对话历史和上下文
- 支持流式响应和实时通信
- 集成多种AI模型和服务

**核心服务**：
- `ChatService`：主要聊天逻辑
- `OpenAIChatService`：OpenAI模型调用
- `NetSearchService`：网络搜索功能

### 2. 用户模块 (User Module)
**位置**：`service/src/modules/user/`

**主要功能**：
- 用户注册、登录、认证
- 用户信息管理
- 权限控制和角色管理
- 实名认证功能

### 3. 模型管理模块 (Models Module)
**位置**：`service/src/modules/models/`

**主要功能**：
- AI模型配置和管理
- API密钥管理
- 模型使用统计
- 负载均衡和故障转移

### 4. 支付模块 (Pay Module)
**位置**：`service/src/modules/pay/`

**主要功能**：
- 多种支付方式集成
- 支付回调处理
- 订单状态管理
- 支付安全验证

### 5. 积分系统 (UserBalance Module)
**位置**：`service/src/modules/userBalance/`

**主要功能**：
- 用户积分管理
- 消费记录追踪
- 套餐和充值处理
- 积分计算和扣除

## 📊 数据库设计

### 核心数据表

#### 用户相关
- `users`：用户基本信息
- `user_balance`：用户积分余额
- `account_log`：积分变动记录
- `verification`：验证码记录

#### 聊天相关
- `chat_log`：聊天记录
- `chat_group`：聊天分组
- `models`：AI模型配置

#### 商业相关
- `order`：订单信息
- `crami_package`：套餐配置
- `config`：系统配置

#### 应用相关
- `app`：应用配置
- `app_cats`：应用分类
- `user_apps`：用户应用关联

#### 系统相关
- `bad_words`：敏感词库
- `auto_reply`：自动回复
- `violation_log`：违规记录

## ⚙️ 配置说明

### 环境变量配置
主要配置文件：`service/.env`

```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASS=123456
DB_DATABASE=99ai

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 服务配置
PORT=9520
ADMIN_SERVE_ROOT=/admin

# 开发模式
ISDEV=false
```

### PM2配置
配置文件：`AIWebQuickDeploy/pm2.conf.json`

```json
{
  "apps": [
    {
      "name": "99AI",
      "script": "./dist/main.js",
      "instances": 1,
      "exec_mode": "cluster",
      "watch": false,
      "max_memory_restart": "1G",
      "env": {
        "NODE_ENV": "production"
      }
    }
  ]
}
```

## 🚀 部署指南

### 快速部署

#### 方式一：Node.js 部署

1. **环境准备**
```bash
# 安装 Node.js 18+
nvm install 22
nvm use 22

# 安装全局依赖
npm install -g pm2 pnpm
```

2. **数据库准备**
```bash
# 安装 MySQL 8.0 和 Redis
# 创建数据库
CREATE DATABASE 99ai CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

3. **项目部署**
```bash
# 进入部署目录
cd AIWebQuickDeploy

# 复制环境配置
cp .env.example .env

# 编辑配置文件
vim .env

# 安装依赖
pnpm install

# 启动服务
pnpm start

# 查看日志
pnpm logs
```

#### 方式二：Docker 部署

1. **安装 Docker**
```bash
curl -fsSL https://get.docker.com | bash -s docker
```

2. **启动服务**
```bash
cd AIWebQuickDeploy
docker-compose up -d
```

3. **查看状态**
```bash
docker-compose ps
docker-compose logs
```

### 一键部署脚本

项目根目录提供了自动化部署脚本：

```bash
./deploy.sh
```

脚本功能：
- Node.js 全新部署：自动安装环境、生成配置、安装依赖并启动服务
- Node.js 升级：拉取最新代码，更新依赖并重启服务
- Docker-compose 部署：创建 MySQL、Redis 容器及 99AI 服务
- Docker-compose 升级：停止旧版本容器，重新构建镜像并启动

### 生产环境优化

#### Nginx 反向代理配置
```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 前端静态文件
    location / {
        proxy_pass http://localhost:9520;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # API 接口
    location /api/ {
        proxy_pass http://localhost:9520;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 流式响应配置
        proxy_buffering off;
        proxy_cache off;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # 文件上传
    location /file/ {
        proxy_pass http://localhost:9520;
        client_max_body_size 100M;
    }
}
```

#### SSL 证书配置
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/your/cert.pem;
    ssl_certificate_key /path/to/your/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
    ssl_prefer_server_ciphers on;

    # 其他配置同上...
}
```

## 🛠️ 开发指南

### 开发环境搭建

#### 1. 克隆项目
```bash
git clone https://github.com/vastxie/99AI.git
cd 99AI
```

#### 2. 后端开发
```bash
cd service

# 安装依赖
pnpm install

# 配置环境变量
cp .env.example .env
vim .env

# 启动开发服务
pnpm dev
```

#### 3. 前端开发

**用户界面开发**
```bash
cd chat

# 安装依赖
pnpm install

# 启动开发服务
pnpm dev
```

**管理后台开发**
```bash
cd admin

# 安装依赖
pnpm install

# 启动开发服务
pnpm dev
```

### 项目构建

#### 一键构建
```bash
# 在项目根目录执行
./build.sh
```

构建流程：
1. 构建管理后台 (`admin/dist`)
2. 构建用户界面 (`chat/dist`)
3. 构建后端服务 (`service/dist`)
4. 复制文件到部署目录 (`AIWebQuickDeploy`)

#### 分别构建
```bash
# 构建后端
cd service && pnpm build

# 构建前端
cd admin && pnpm build
cd chat && pnpm build
```

### 代码规范

#### 提交规范
```bash
# 安装提交工具
npm install -g commitizen cz-git

# 规范化提交
git cz
```

#### 代码格式化
```bash
# 后端代码格式化
cd service && pnpm format

# 前端代码格式化
cd admin && pnpm format
cd chat && pnpm format
```

### 开发调试

#### 后端调试
```bash
# 开发模式启动
pnpm dev

# 调试模式启动
pnpm start:debug

# 查看 Swagger API 文档
# http://localhost:9520/api-docs
```

#### 前端调试
```bash
# 启动开发服务器
pnpm dev

# 类型检查
pnpm type-check

# 构建检查
pnpm build-check
```

## 📡 API 接口文档

### 认证接口

#### 用户登录
```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "<EMAIL>",
  "password": "password123"
}
```

#### 获取用户信息
```http
GET /api/auth/getInfo
Authorization: Bearer <token>
```

### 聊天接口

#### 发送消息
```http
POST /api/chatgpt/chat-process
Authorization: Bearer <token>
Content-Type: application/json

{
  "prompt": "你好，请介绍一下自己",
  "model": "gpt-4o-mini",
  "modelName": "GPT-4o Mini",
  "options": {
    "groupId": 1,
    "usingNetwork": false,
    "usingMcpTool": false
  }
}
```

#### TTS 语音合成
```http
POST /api/chatgpt/tts-process
Authorization: Bearer <token>
Content-Type: application/json

{
  "text": "要转换的文本",
  "voice": "alloy"
}
```

### 用户管理接口

#### 查询用户列表
```http
GET /api/user/queryAll?page=1&size=10
Authorization: Bearer <token>
```

#### 更新用户状态
```http
POST /api/user/updateStatus
Authorization: Bearer <token>
Content-Type: application/json

{
  "id": 1,
  "status": 1
}
```

### 模型管理接口

#### 查询模型列表
```http
GET /api/models/query?page=1&size=10
Authorization: Bearer <token>
```

#### 设置模型
```http
POST /api/models/setModel
Authorization: Bearer <token>
Content-Type: application/json

{
  "modelName": "gpt-4o",
  "model": "gpt-4o",
  "key": ["sk-xxx"],
  "proxyUrl": "https://api.openai.com",
  "deduct": 1,
  "status": 1
}
```

### 支付接口

#### 创建订单
```http
POST /api/order/create
Authorization: Bearer <token>
Content-Type: application/json

{
  "goodsId": 1,
  "payType": "wechat"
}
```

#### 查询订单
```http
GET /api/order/query?page=1&size=10
Authorization: Bearer <token>
```

### 文件上传接口

#### 上传文件
```http
POST /api/upload/file
Authorization: Bearer <token>
Content-Type: multipart/form-data

file: <binary>
```

### 响应格式

所有 API 接口都遵循统一的响应格式：

```json
{
  "code": 200,
  "success": true,
  "message": "请求成功",
  "data": {
    // 具体数据
  }
}
```

错误响应：
```json
{
  "code": 400,
  "success": false,
  "message": "错误信息",
  "data": null
}
```

## 🗄️ 数据库详细设计

### 数据库表结构

#### 用户系统表

**users - 用户基本信息表**
```sql
CREATE TABLE `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(100) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像',
  `role` varchar(20) DEFAULT 'user' COMMENT '角色',
  `status` tinyint DEFAULT '1' COMMENT '状态 1正常 0禁用',
  `openId` varchar(100) DEFAULT NULL COMMENT '微信openId',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `realName` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `idCard` varchar(20) DEFAULT NULL COMMENT '身份证号',
  `consecutiveDays` int DEFAULT '0' COMMENT '连续签到天数',
  `createdAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
);
```

**user_balance - 用户余额表**
```sql
CREATE TABLE `user_balance` (
  `id` int NOT NULL AUTO_INCREMENT,
  `userId` int NOT NULL COMMENT '用户ID',
  `balance` decimal(10,2) DEFAULT '0.00' COMMENT '余额',
  `usedTokens` bigint DEFAULT '0' COMMENT '已使用token数',
  `paintCount` int DEFAULT '0' COMMENT '绘画次数',
  `createdAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `userId` (`userId`)
);
```

#### 聊天系统表

**chat_log - 聊天记录表**
```sql
CREATE TABLE `chat_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `userId` int NOT NULL COMMENT '用户ID',
  `groupId` int DEFAULT NULL COMMENT '分组ID',
  `appId` int DEFAULT NULL COMMENT '应用ID',
  `prompt` text COMMENT '用户输入',
  `content` longtext COMMENT 'AI回复内容',
  `model` varchar(100) DEFAULT NULL COMMENT '使用的模型',
  `modelName` varchar(100) DEFAULT NULL COMMENT '模型显示名称',
  `tokens` int DEFAULT '0' COMMENT '消耗token数',
  `status` tinyint DEFAULT '1' COMMENT '状态 1成功 2失败',
  `fileUrl` varchar(500) DEFAULT NULL COMMENT '文件URL',
  `imageUrl` varchar(500) DEFAULT NULL COMMENT '图片URL',
  `createdAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `userId` (`userId`),
  KEY `groupId` (`groupId`)
);
```

**chat_group - 聊天分组表**
```sql
CREATE TABLE `chat_group` (
  `id` int NOT NULL AUTO_INCREMENT,
  `userId` int NOT NULL COMMENT '用户ID',
  `title` varchar(200) DEFAULT '新对话' COMMENT '对话标题',
  `isSticky` tinyint DEFAULT '0' COMMENT '是否置顶',
  `appId` int DEFAULT NULL COMMENT '关联应用ID',
  `config` json DEFAULT NULL COMMENT '对话配置',
  `createdAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `userId` (`userId`)
);
```

#### 模型管理表

**models - AI模型配置表**
```sql
CREATE TABLE `models` (
  `id` int NOT NULL AUTO_INCREMENT,
  `modelName` varchar(100) NOT NULL COMMENT '模型名称',
  `model` varchar(100) NOT NULL COMMENT '模型标识',
  `key` varchar(500) NOT NULL COMMENT 'API密钥',
  `proxyUrl` varchar(200) DEFAULT NULL COMMENT '代理地址',
  `modelType` tinyint DEFAULT '1' COMMENT '模型类型',
  `deduct` decimal(10,4) DEFAULT '1.0000' COMMENT '扣费比例',
  `status` tinyint DEFAULT '1' COMMENT '状态',
  `maxTokens` int DEFAULT '4000' COMMENT '最大token数',
  `timeout` int DEFAULT '300' COMMENT '超时时间',
  `useCount` bigint DEFAULT '0' COMMENT '使用次数',
  `useToken` bigint DEFAULT '0' COMMENT '使用token总数',
  `createdAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

#### 商业系统表

**order - 订单表**
```sql
CREATE TABLE `order` (
  `id` int NOT NULL AUTO_INCREMENT,
  `userId` int NOT NULL COMMENT '用户ID',
  `orderId` varchar(50) NOT NULL COMMENT '订单号',
  `goodsId` int NOT NULL COMMENT '商品ID',
  `goodsName` varchar(200) NOT NULL COMMENT '商品名称',
  `price` decimal(10,2) NOT NULL COMMENT '订单金额',
  `payType` varchar(20) DEFAULT 'wechat' COMMENT '支付方式',
  `payPlatform` varchar(20) DEFAULT 'wechat' COMMENT '支付平台',
  `status` tinyint DEFAULT '0' COMMENT '订单状态 0待支付 1已支付 2已取消',
  `payTime` datetime DEFAULT NULL COMMENT '支付时间',
  `createdAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `orderId` (`orderId`),
  KEY `userId` (`userId`)
);
```

**crami_package - 套餐配置表**
```sql
CREATE TABLE `crami_package` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '套餐名称',
  `des` varchar(500) DEFAULT NULL COMMENT '套餐描述',
  `price` decimal(10,2) NOT NULL COMMENT '套餐价格',
  `tokenCount` bigint DEFAULT '0' COMMENT 'token数量',
  `paintCount` int DEFAULT '0' COMMENT '绘画次数',
  `days` int DEFAULT '0' COMMENT '有效天数',
  `status` tinyint DEFAULT '1' COMMENT '状态',
  `sort` int DEFAULT '0' COMMENT '排序',
  `createdAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

#### 应用系统表

**app - 应用配置表**
```sql
CREATE TABLE `app` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '应用名称',
  `des` varchar(500) DEFAULT NULL COMMENT '应用描述',
  `preset` text COMMENT '预设提示词',
  `coverImg` varchar(500) DEFAULT NULL COMMENT '封面图片',
  `demoData` json DEFAULT NULL COMMENT '示例数据',
  `catId` int DEFAULT NULL COMMENT '分类ID',
  `status` tinyint DEFAULT '1' COMMENT '状态',
  `public` tinyint DEFAULT '1' COMMENT '是否公开',
  `order` int DEFAULT '0' COMMENT '排序',
  `createdAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

#### 系统配置表

**config - 系统配置表**
```sql
CREATE TABLE `config` (
  `id` int NOT NULL AUTO_INCREMENT,
  `configKey` varchar(100) NOT NULL COMMENT '配置键',
  `configVal` text COMMENT '配置值',
  `public` tinyint DEFAULT '0' COMMENT '是否公开',
  `encry` tinyint DEFAULT '0' COMMENT '是否加密',
  `createdAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `configKey` (`configKey`)
);
```

### 数据库索引优化

#### 主要索引
```sql
-- 用户表索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_users_status ON users(status);

-- 聊天记录索引
CREATE INDEX idx_chatlog_user_group ON chat_log(userId, groupId);
CREATE INDEX idx_chatlog_created ON chat_log(createdAt);
CREATE INDEX idx_chatlog_model ON chat_log(model);

-- 订单表索引
CREATE INDEX idx_order_user_status ON `order`(userId, status);
CREATE INDEX idx_order_created ON `order`(createdAt);
CREATE INDEX idx_order_pay_time ON `order`(payTime);

-- 模型表索引
CREATE INDEX idx_models_status ON models(status);
CREATE INDEX idx_models_type ON models(modelType);
```

## 🔧 系统配置详解

### 环境变量配置

#### 数据库配置
```bash
# MySQL 数据库配置
DB_HOST=localhost          # 数据库主机
DB_PORT=3306              # 数据库端口
DB_USER=root              # 数据库用户名
DB_PASS=123456            # 数据库密码
DB_DATABASE=99ai          # 数据库名称
DB_SYNC=false             # 是否同步表结构

# Redis 配置
REDIS_HOST=localhost      # Redis主机
REDIS_PORT=6379          # Redis端口
REDIS_PASSWORD=          # Redis密码
REDIS_DB=0               # Redis数据库编号
```

#### 服务配置
```bash
# 服务端口配置
PORT=9520                # 服务端口
ADMIN_SERVE_ROOT=/admin  # 管理后台路径

# 开发模式
ISDEV=false              # 是否开发模式
NODE_ENV=production      # 运行环境

# JWT配置
JWT_SECRET=              # JWT密钥(自动生成)
JWT_EXPIRESIN=7d         # JWT过期时间
```

#### 文件存储配置
```bash
# 本地存储
UPLOAD_TYPE=local        # 上传类型
UPLOAD_PATH=./public/file # 上传路径

# 阿里云OSS
ALI_OSS_ACCESS_KEY=      # AccessKey
ALI_OSS_ACCESS_SECRET=   # AccessSecret
ALI_OSS_BUCKET=          # Bucket名称
ALI_OSS_REGION=          # 地域
ALI_OSS_DOMAIN=          # 自定义域名

# 腾讯云COS
TENCENT_COS_SECRET_ID=   # SecretId
TENCENT_COS_SECRET_KEY=  # SecretKey
TENCENT_COS_BUCKET=      # Bucket名称
TENCENT_COS_REGION=      # 地域

# AWS S3
AWS_ACCESS_KEY_ID=       # AccessKey
AWS_SECRET_ACCESS_KEY=   # SecretKey
AWS_REGION=              # 地域
AWS_BUCKET=              # Bucket名称
```

#### 邮件服务配置
```bash
# SMTP配置
MAILER_HOST=smtp.qq.com  # SMTP服务器
MAILER_PORT=587          # SMTP端口
MAILER_USER=             # 邮箱账号
MAILER_PASS=             # 邮箱密码
MAILER_FROM=             # 发件人
```

#### 支付配置
```bash
# 微信支付
WX_APPID=                # 微信AppID
WX_MCHID=                # 商户号
WX_API_KEY=              # API密钥
WX_SERIAL_NO=            # 证书序列号
WX_PRIVATE_KEY=          # 私钥路径

# 支付宝
ALIPAY_APPID=            # 应用ID
ALIPAY_PRIVATE_KEY=      # 应用私钥
ALIPAY_PUBLIC_KEY=       # 支付宝公钥

# 易支付
EPAY_PID=                # 商户ID
EPAY_SECRET=             # 商户密钥
EPAY_API_URL=            # API地址
EPAY_NOTIFY_URL=         # 回调地址
EPAY_RETURN_URL=         # 返回地址
```

### 系统配置项

#### 基础配置
- `siteName`: 网站名称
- `siteDescription`: 网站描述
- `siteKeywords`: 网站关键词
- `siteLogo`: 网站Logo
- `siteIcon`: 网站图标

#### 用户配置
- `registerEnabled`: 是否开放注册
- `registerReward`: 注册奖励积分
- `inviteReward`: 邀请奖励积分
- `signInReward`: 签到奖励积分
- `maxSignInDays`: 最大连续签到天数

#### AI模型配置
- `defaultModel`: 默认AI模型
- `maxTokens`: 最大token限制
- `maxHistoryLength`: 最大历史记录长度
- `enableNetwork`: 是否启用联网搜索
- `enableUpload`: 是否启用文件上传

#### 安全配置
- `enableCaptcha`: 是否启用验证码
- `maxLoginAttempts`: 最大登录尝试次数
- `lockoutDuration`: 锁定时长
- `enableRateLimit`: 是否启用频率限制
- `rateLimitMax`: 频率限制次数

## 🔍 常见问题与解决方案

### 部署相关问题

#### Q1: Docker 部署时数据库连接失败
**问题描述**: 容器启动后无法连接到 MySQL 数据库

**解决方案**:
1. 检查 `.env.docker` 文件中的数据库配置
2. 确保 MySQL 容器已正常启动
3. 检查网络连接和端口映射
```bash
# 查看容器状态
docker-compose ps

# 查看容器日志
docker-compose logs mysql
docker-compose logs 99ai
```

#### Q2: PM2 进程启动失败
**问题描述**: 使用 PM2 启动服务时报错

**解决方案**:
1. 检查 Node.js 版本是否符合要求 (18+)
2. 确保依赖已正确安装
3. 检查环境变量配置
```bash
# 重新安装依赖
pnpm install

# 检查 PM2 状态
pm2 status

# 查看详细日志
pm2 logs 99AI
```

#### Q3: 前端页面无法访问
**问题描述**: 部署后前端页面显示 404 或无法加载

**解决方案**:
1. 检查静态文件是否正确复制到 `public` 目录
2. 确认 Nginx 配置正确
3. 检查服务端口是否正常监听
```bash
# 检查端口占用
netstat -tlnp | grep 9520

# 重新构建前端
cd admin && pnpm build
cd chat && pnpm build
```

### 功能相关问题

#### Q4: AI 模型调用失败
**问题描述**: 聊天时提示模型调用失败

**解决方案**:
1. 检查 API 密钥是否正确配置
2. 确认代理地址是否可访问
3. 检查模型配置和状态
```bash
# 在管理后台检查模型配置
# 路径: /admin -> 模型管理 -> 模型列表
```

#### Q5: 文件上传失败
**问题描述**: 上传文件时报错或无法显示

**解决方案**:
1. 检查上传目录权限
2. 确认文件大小限制
3. 检查存储配置
```bash
# 检查目录权限
chmod 755 ./public/file

# 检查磁盘空间
df -h
```

#### Q6: 支付回调失败
**问题描述**: 支付完成后订单状态未更新

**解决方案**:
1. 检查支付平台回调地址配置
2. 确认回调接口可正常访问
3. 查看支付日志
```bash
# 查看支付相关日志
pm2 logs 99AI | grep pay
```

### 性能优化问题

#### Q7: 响应速度慢
**问题描述**: 系统响应速度较慢

**解决方案**:
1. 优化数据库查询和索引
2. 启用 Redis 缓存
3. 配置 CDN 加速
```sql
-- 添加必要的数据库索引
CREATE INDEX idx_chatlog_user_created ON chat_log(userId, createdAt);
CREATE INDEX idx_users_status_created ON users(status, createdAt);
```

#### Q8: 内存占用过高
**问题描述**: 服务器内存使用率过高

**解决方案**:
1. 调整 PM2 配置
2. 优化数据库连接池
3. 清理无用的聊天记录
```json
// pm2.conf.json 优化配置
{
  "apps": [{
    "name": "99AI",
    "script": "./dist/main.js",
    "instances": 1,
    "max_memory_restart": "512M",
    "node_args": "--max-old-space-size=512"
  }]
}
```

## 🚀 性能优化建议

### 数据库优化

#### 1. 索引优化
```sql
-- 聊天记录查询优化
CREATE INDEX idx_chatlog_user_group_created ON chat_log(userId, groupId, createdAt);

-- 用户查询优化
CREATE INDEX idx_users_role_status ON users(role, status);

-- 订单查询优化
CREATE INDEX idx_order_user_status_created ON `order`(userId, status, createdAt);
```

#### 2. 分表策略
```sql
-- 按月分表聊天记录
CREATE TABLE chat_log_202401 LIKE chat_log;
CREATE TABLE chat_log_202402 LIKE chat_log;
-- ...
```

#### 3. 数据清理
```sql
-- 清理过期的验证码记录
DELETE FROM verification WHERE createdAt < DATE_SUB(NOW(), INTERVAL 1 DAY);

-- 清理过期的聊天记录（可选）
DELETE FROM chat_log WHERE createdAt < DATE_SUB(NOW(), INTERVAL 6 MONTH);
```

### 缓存优化

#### 1. Redis 缓存策略
```typescript
// 用户信息缓存
const userCacheKey = `user:${userId}`;
await redis.setex(userCacheKey, 3600, JSON.stringify(userInfo));

// 模型配置缓存
const modelCacheKey = 'models:config';
await redis.setex(modelCacheKey, 1800, JSON.stringify(modelsConfig));

// 系统配置缓存
const configCacheKey = 'system:config';
await redis.setex(configCacheKey, 3600, JSON.stringify(systemConfig));
```

#### 2. 前端缓存
```typescript
// 静态资源缓存
// vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          ui: ['element-plus', '@element-plus/icons-vue']
        }
      }
    }
  }
});
```

### 服务器优化

#### 1. Nginx 配置优化
```nginx
# 启用 gzip 压缩
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

# 静态文件缓存
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# API 接口缓存
location /api/config/public {
    proxy_pass http://localhost:9520;
    proxy_cache_valid 200 10m;
}
```

#### 2. PM2 集群模式
```json
{
  "apps": [{
    "name": "99AI",
    "script": "./dist/main.js",
    "instances": "max",
    "exec_mode": "cluster",
    "max_memory_restart": "1G"
  }]
}
```

## 📈 监控与日志

### 系统监控

#### 1. PM2 监控
```bash
# 安装 PM2 监控
pm2 install pm2-server-monit

# 查看实时监控
pm2 monit
```

#### 2. 数据库监控
```sql
-- 查看慢查询
SHOW VARIABLES LIKE 'slow_query_log';
SHOW VARIABLES LIKE 'long_query_time';

-- 查看连接数
SHOW STATUS LIKE 'Threads_connected';
SHOW STATUS LIKE 'Max_used_connections';
```

#### 3. Redis 监控
```bash
# Redis 信息
redis-cli info memory
redis-cli info stats
redis-cli info clients
```

### 日志管理

#### 1. 应用日志
```typescript
// 自定义日志配置
// service/src/common/logger/custom-logger.service.ts
export class CustomLoggerService extends Logger {
  log(message: string, context?: string) {
    super.log(`[${new Date().toISOString()}] ${message}`, context);
  }

  error(message: string, trace?: string, context?: string) {
    super.error(`[${new Date().toISOString()}] ${message}`, trace, context);
  }
}
```

#### 2. 访问日志
```nginx
# Nginx 访问日志格式
log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                '$status $body_bytes_sent "$http_referer" '
                '"$http_user_agent" "$http_x_forwarded_for" '
                '$request_time $upstream_response_time';

access_log /var/log/nginx/99ai_access.log main;
```

## 🔒 安全加固

### 1. 数据库安全
```sql
-- 创建专用数据库用户
CREATE USER '99ai'@'localhost' IDENTIFIED BY 'strong_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON 99ai.* TO '99ai'@'localhost';
FLUSH PRIVILEGES;
```

### 2. 服务器安全
```bash
# 防火墙配置
ufw allow 22/tcp
ufw allow 80/tcp
ufw allow 443/tcp
ufw deny 3306/tcp
ufw deny 6379/tcp
ufw enable

# 定期更新系统
apt update && apt upgrade -y
```

### 3. 应用安全
```typescript
// 请求频率限制
@UseGuards(ThrottlerGuard)
@Throttle(10, 60) // 每分钟最多10次请求
export class ChatController {
  // ...
}

// 输入验证
@IsString()
@Length(1, 1000)
@IsNotEmpty()
prompt: string;
```

## 📚 扩展开发

### 1. 自定义插件开发
```typescript
// 创建自定义插件
// service/src/modules/plugins/custom-plugin.service.ts
@Injectable()
export class CustomPluginService {
  async processRequest(input: string): Promise<string> {
    // 自定义处理逻辑
    return processedResult;
  }
}
```

### 2. 新增AI模型支持
```typescript
// 添加新的AI模型适配器
// service/src/modules/aiTool/adapters/custom-ai.service.ts
@Injectable()
export class CustomAIService {
  async chat(messages: any[], options: any): Promise<any> {
    // 实现自定义AI模型调用
  }
}
```

### 3. 前端组件扩展
```vue
<!-- 创建自定义聊天组件 -->
<!-- chat/src/components/CustomChatComponent.vue -->
<template>
  <div class="custom-chat">
    <!-- 自定义聊天界面 -->
  </div>
</template>

<script setup lang="ts">
// 组件逻辑
</script>
```

## 📞 技术支持

### 官方资源
- **项目地址**: https://github.com/vastxie/99AI
- **在线文档**: https://docs.lightai.cloud/
- **演示地址**: https://99ai.lightai.cloud/

### 社区支持
- **GitHub Issues**: 提交Bug报告和功能请求
- **微信交流群**: 扫描README中的二维码加入
- **技术讨论**: 参与项目讨论和经验分享

### 商业支持
- **开发版授权**: 获取更多高级功能
- **定制开发**: 根据需求定制功能
- **技术咨询**: 专业的技术支持服务

---

## 📄 许可证

本项目采用 [Apache 2.0](LICENSE) 开源协议，支持商业使用。使用本项目时请保留项目署名和链接。

**版权声明**: 版权由原作者所有，项目支持商用，闭源分发需授权许可。

---

*最后更新时间: 2024年12月*

*文档版本: v1.0*
