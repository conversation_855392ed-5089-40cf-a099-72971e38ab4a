#!/usr/bin/env node

/**
 * 环境检查脚本
 * 在启动前检查所有必要的环境配置
 */

const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');

console.log('======================================');
console.log('        JianfaAI 环境检查');
console.log('======================================\n');

// 加载环境变量
require('dotenv').config();

let hasError = false;

// 检查必要文件
function checkFiles() {
  console.log('📁 检查必要文件...');
  
  const requiredFiles = [
    'dist/main.js',
    '.env'
  ];
  
  const requiredDirs = [
    'logs',
    'public',
    'public/file'
  ];
  
  // 检查文件
  for (const file of requiredFiles) {
    if (!fs.existsSync(file)) {
      console.error(`❌ 缺少文件: ${file}`);
      hasError = true;
    } else {
      console.log(`✅ 文件存在: ${file}`);
    }
  }
  
  // 创建必要目录
  for (const dir of requiredDirs) {
    if (!fs.existsSync(dir)) {
      try {
        fs.mkdirSync(dir, { recursive: true });
        console.log(`✅ 创建目录: ${dir}`);
      } catch (error) {
        console.error(`❌ 创建目录失败: ${dir} - ${error.message}`);
        hasError = true;
      }
    } else {
      console.log(`✅ 目录存在: ${dir}`);
    }
  }
}

// 检查环境变量
function checkEnvVars() {
  console.log('\n🔧 检查环境变量...');
  
  const requiredVars = [
    'DB_HOST',
    'DB_PORT', 
    'DB_USER',
    'DB_DATABASE'
  ];
  
  const optionalVars = [
    'DB_PASS',
    'REDIS_HOST',
    'REDIS_PORT',
    'PORT'
  ];
  
  // 检查必需变量
  for (const varName of requiredVars) {
    if (!process.env[varName]) {
      console.error(`❌ 缺少环境变量: ${varName}`);
      hasError = true;
    } else {
      console.log(`✅ ${varName}: ${process.env[varName]}`);
    }
  }
  
  // 检查可选变量
  for (const varName of optionalVars) {
    if (process.env[varName]) {
      console.log(`✅ ${varName}: ${process.env[varName]}`);
    } else {
      console.log(`⚠️  ${varName}: 未设置（使用默认值）`);
    }
  }
}

// 检查数据库连接
async function checkDatabase() {
  console.log('\n🗄️  检查数据库连接...');
  
  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT),
      user: process.env.DB_USER,
      password: process.env.DB_PASS || '',
      database: process.env.DB_DATABASE
    });
    
    await connection.execute('SELECT 1');
    await connection.end();
    
    console.log('✅ 数据库连接成功');
  } catch (error) {
    console.error(`❌ 数据库连接失败: ${error.message}`);
    hasError = true;
  }
}

// 检查端口占用
function checkPort() {
  console.log('\n🌐 检查端口占用...');
  
  const port = process.env.PORT || 9520;
  const net = require('net');
  
  return new Promise((resolve) => {
    const server = net.createServer();
    
    server.listen(port, () => {
      server.close(() => {
        console.log(`✅ 端口 ${port} 可用`);
        resolve();
      });
    });
    
    server.on('error', (err) => {
      if (err.code === 'EADDRINUSE') {
        console.error(`❌ 端口 ${port} 已被占用`);
        hasError = true;
      }
      resolve();
    });
  });
}

// 主检查函数
async function main() {
  try {
    checkFiles();
    checkEnvVars();
    
    if (!hasError) {
      await checkDatabase();
      await checkPort();
    }
    
    console.log('\n======================================');
    if (hasError) {
      console.log('❌ 环境检查失败，请修复上述问题后重试');
      process.exit(1);
    } else {
      console.log('✅ 环境检查通过，可以启动服务');
      process.exit(0);
    }
  } catch (error) {
    console.error(`❌ 检查过程中出错: ${error.message}`);
    process.exit(1);
  }
}

main();
