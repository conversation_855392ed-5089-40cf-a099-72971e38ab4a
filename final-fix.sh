#!/bin/bash

# 最终修复脚本
# 解决所有已知的构建问题

echo "======================================="
echo "        JianfaAI 最终修复脚本"
echo "======================================="

# 检查pnpm
command -v pnpm >/dev/null 2>&1 || { echo "❌ 错误: 需要安装 pnpm"; exit 1; }

echo "🔧 开始最终修复..."

# 1. 检查关键文件的语法
echo "🔍 检查关键文件语法..."

# 检查main.ts
if ! npx tsc --noEmit service/src/main.ts 2>/dev/null; then
    echo "❌ main.ts 仍有语法错误"
    exit 1
else
    echo "✅ main.ts 语法正确"
fi

# 检查upload.service.ts
if ! npx tsc --noEmit service/src/modules/upload/upload.service.ts 2>/dev/null; then
    echo "❌ upload.service.ts 仍有语法错误"
    exit 1
else
    echo "✅ upload.service.ts 语法正确"
fi

# 2. 修复service目录
echo "📦 修复后端服务..."
cd service/

# 清理并重新安装依赖
echo "🧹 清理后端依赖..."
rm -f pnpm-lock.yaml
rm -rf node_modules

echo "📦 重新安装后端依赖..."
if pnpm install; then
    echo "✅ 后端依赖安装成功"
else
    echo "❌ 后端依赖安装失败"
    exit 1
fi

# 尝试构建
echo "🔨 构建后端服务..."
if pnpm build; then
    echo "✅ 后端构建成功"
else
    echo "❌ 后端构建失败"
    exit 1
fi

cd ..

# 3. 修复admin目录
echo "📦 修复管理后台..."
cd admin/

# 清理并重新安装依赖
echo "🧹 清理管理后台依赖..."
rm -f pnpm-lock.yaml
rm -rf node_modules

echo "📦 重新安装管理后台依赖..."
if pnpm install; then
    echo "✅ 管理后台依赖安装成功"
else
    echo "❌ 管理后台依赖安装失败"
    exit 1
fi

# 尝试构建
echo "🔨 构建管理后台..."
if pnpm build; then
    echo "✅ 管理后台构建成功"
else
    echo "❌ 管理后台构建失败"
    exit 1
fi

cd ..

# 4. 修复chat目录
echo "📦 修复用户界面..."
cd chat/

# 清理并重新安装依赖
echo "🧹 清理用户界面依赖..."
rm -f pnpm-lock.yaml
rm -rf node_modules

echo "📦 重新安装用户界面依赖..."
if pnpm install; then
    echo "✅ 用户界面依赖安装成功"
else
    echo "❌ 用户界面依赖安装失败"
    exit 1
fi

# 尝试构建
echo "🔨 构建用户界面..."
if pnpm build; then
    echo "✅ 用户界面构建成功"
else
    echo "❌ 用户界面构建失败"
    exit 1
fi

cd ..

# 5. 验证所有构建产物
echo "🔍 验证构建产物..."

if [ ! -f "service/dist/main.js" ]; then
    echo "❌ 后端构建产物不存在"
    exit 1
fi

if [ ! -f "admin/dist/index.html" ]; then
    echo "❌ 管理后台构建产物不存在"
    exit 1
fi

if [ ! -f "chat/dist/index.html" ]; then
    echo "❌ 用户界面构建产物不存在"
    exit 1
fi

echo "✅ 所有构建产物验证通过"

# 6. 清理临时文件
echo "🧹 清理临时文件..."
find . -name "*.backup" -delete 2>/dev/null || true
find . -name "*.log" -delete 2>/dev/null || true

echo ""
echo "======================================="
echo "✅ 最终修复完成！"
echo ""
echo "所有问题已解决："
echo "  ✅ main.ts 语法错误已修复"
echo "  ✅ upload.service.ts s3Status 错误已修复"
echo "  ✅ pnpm-lock.yaml 不匹配问题已解决"
echo "  ✅ 所有项目构建成功"
echo ""
echo "现在可以运行完整构建："
echo "  ./build.sh"
echo "======================================="
