#!/usr/bin/env node

/**
 * 部署后验证脚本
 * 验证部署是否成功，所有功能是否正常
 */

const fs = require('fs');
const path = require('path');
const http = require('http');

console.log('======================================');
console.log('        JianfaAI 部署后验证');
console.log('======================================\n');

let hasError = false;

// 加载环境变量
require('dotenv').config();

// 检查部署文件
function checkDeployedFiles() {
  console.log('📁 检查部署文件...');
  
  const requiredFiles = [
    'dist/main.js',
    'public/admin/index.html',
    'public/chat/index.html',
    'package.json',
    'pm2.conf.json'
  ];
  
  const requiredDirs = [
    'dist',
    'public',
    'public/admin',
    'public/chat',
    'logs'
  ];
  
  // 检查目录
  for (const dir of requiredDirs) {
    if (!fs.existsSync(dir)) {
      console.error(`❌ 缺少目录: ${dir}`);
      hasError = true;
    } else {
      console.log(`✅ 目录存在: ${dir}`);
    }
  }
  
  // 检查文件
  for (const file of requiredFiles) {
    if (!fs.existsSync(file)) {
      console.error(`❌ 缺少文件: ${file}`);
      hasError = true;
    } else {
      const stats = fs.statSync(file);
      console.log(`✅ 文件存在: ${file} (${(stats.size / 1024).toFixed(1)}KB)`);
    }
  }
}

// 检查环境配置
function checkEnvironment() {
  console.log('\n🌍 检查环境配置...');
  
  const requiredEnvVars = [
    'DB_HOST',
    'DB_PORT',
    'DB_USER',
    'DB_DATABASE'
  ];
  
  const optionalEnvVars = [
    'PORT',
    'REDIS_HOST',
    'REDIS_PORT'
  ];
  
  // 检查必需变量
  for (const varName of requiredEnvVars) {
    if (!process.env[varName]) {
      console.error(`❌ 缺少环境变量: ${varName}`);
      hasError = true;
    } else {
      console.log(`✅ ${varName}: ${process.env[varName]}`);
    }
  }
  
  // 检查可选变量
  for (const varName of optionalEnvVars) {
    if (process.env[varName]) {
      console.log(`✅ ${varName}: ${process.env[varName]}`);
    } else {
      console.log(`⚠️  ${varName}: 未设置（使用默认值）`);
    }
  }
}

// 检查PM2配置
function checkPM2Config() {
  console.log('\n⚙️  检查PM2配置...');
  
  try {
    const pm2Config = JSON.parse(fs.readFileSync('pm2.conf.json', 'utf8'));
    
    if (Array.isArray(pm2Config.apps)) {
      const app = pm2Config.apps[0];
      console.log(`✅ 应用名称: ${app.name}`);
      console.log(`✅ 启动脚本: ${app.script}`);
      console.log(`✅ 重启策略: 最大${app.max_restarts}次，延迟${app.restart_delay}ms`);
    } else {
      console.error('❌ PM2配置格式错误');
      hasError = true;
    }
  } catch (error) {
    console.error(`❌ PM2配置读取失败: ${error.message}`);
    hasError = true;
  }
}

// 测试应用启动
function testAppStart() {
  console.log('\n🚀 测试应用启动...');
  
  return new Promise((resolve) => {
    try {
      const { spawn } = require('child_process');
      const child = spawn('node', ['dist/main.js'], {
        stdio: 'pipe',
        timeout: 10000
      });
      
      let output = '';
      let hasStarted = false;
      
      child.stdout.on('data', (data) => {
        output += data.toString();
        if (output.includes('服务启动成功') || output.includes('listening on')) {
          hasStarted = true;
          child.kill();
        }
      });
      
      child.stderr.on('data', (data) => {
        output += data.toString();
      });
      
      child.on('close', (code) => {
        if (hasStarted) {
          console.log('✅ 应用可以正常启动');
        } else {
          console.error('❌ 应用启动失败');
          console.error('输出:', output);
          hasError = true;
        }
        resolve();
      });
      
      child.on('error', (error) => {
        console.error(`❌ 启动测试失败: ${error.message}`);
        hasError = true;
        resolve();
      });
      
      // 10秒超时
      setTimeout(() => {
        if (!hasStarted) {
          child.kill();
          console.error('❌ 应用启动超时');
          hasError = true;
        }
        resolve();
      }, 10000);
      
    } catch (error) {
      console.error(`❌ 启动测试异常: ${error.message}`);
      hasError = true;
      resolve();
    }
  });
}

// 检查端口可用性
function checkPort() {
  console.log('\n🌐 检查端口可用性...');
  
  const port = process.env.PORT || 9520;
  
  return new Promise((resolve) => {
    const server = http.createServer();
    
    server.listen(port, () => {
      console.log(`✅ 端口 ${port} 可用`);
      server.close(() => resolve());
    });
    
    server.on('error', (err) => {
      if (err.code === 'EADDRINUSE') {
        console.warn(`⚠️  端口 ${port} 已被占用（可能是服务正在运行）`);
      } else {
        console.error(`❌ 端口检查失败: ${err.message}`);
        hasError = true;
      }
      resolve();
    });
  });
}

// 检查文件权限
function checkPermissions() {
  console.log('\n🔐 检查文件权限...');
  
  const checkPaths = [
    'dist',
    'public',
    'logs'
  ];
  
  for (const checkPath of checkPaths) {
    try {
      // 测试读写权限
      fs.accessSync(checkPath, fs.constants.R_OK | fs.constants.W_OK);
      console.log(`✅ ${checkPath} 权限正常`);
    } catch (error) {
      console.error(`❌ ${checkPath} 权限不足: ${error.message}`);
      hasError = true;
    }
  }
}

// 主验证函数
async function main() {
  try {
    checkDeployedFiles();
    checkEnvironment();
    checkPM2Config();
    checkPermissions();
    await checkPort();
    await testAppStart();
    
    console.log('\n======================================');
    if (hasError) {
      console.log('❌ 部署验证失败，请检查上述问题');
      console.log('\n建议操作:');
      console.log('1. 检查文件是否完整');
      console.log('2. 配置.env文件');
      console.log('3. 确保数据库可连接');
      console.log('4. 检查文件权限');
      process.exit(1);
    } else {
      console.log('✅ 部署验证通过，可以启动服务');
      console.log('\n启动命令:');
      console.log('npm start');
      process.exit(0);
    }
  } catch (error) {
    console.error(`❌ 验证过程中出错: ${error.message}`);
    process.exit(1);
  }
}

main();
