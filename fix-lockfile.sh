#!/bin/bash

# 快速修复lockfile问题的脚本
# 专门解决pnpm-lock.yaml与package.json不匹配的问题

echo "======================================="
echo "        修复 Lockfile 不匹配问题"
echo "======================================="

# 检查pnpm
command -v pnpm >/dev/null 2>&1 || { echo "❌ 错误: 需要安装 pnpm"; exit 1; }

# 修复单个项目的lockfile
fix_project_lockfile() {
    local project_dir=$1
    local project_name=$2
    
    echo "🔧 修复 ${project_name} lockfile..."
    cd "$project_dir"
    
    if [ -f "package.json" ]; then
        # 检查是否有lockfile不匹配问题
        if ! pnpm install --frozen-lockfile 2>/dev/null; then
            echo "⚠️  检测到 ${project_name} lockfile不匹配，正在修复..."
            
            # 备份现有lockfile（如果存在）
            if [ -f "pnpm-lock.yaml" ]; then
                cp pnpm-lock.yaml pnpm-lock.yaml.backup
                echo "📄 已备份现有lockfile为 pnpm-lock.yaml.backup"
            fi
            
            # 删除lockfile和node_modules
            rm -f pnpm-lock.yaml
            rm -rf node_modules
            
            # 清理pnpm缓存
            pnpm store prune 2>/dev/null || true
            
            # 重新安装依赖
            echo "📦 重新安装 ${project_name} 依赖..."
            if pnpm install; then
                echo "✅ ${project_name} lockfile修复成功"
            else
                echo "❌ ${project_name} 依赖安装失败"
                # 如果有备份，尝试恢复
                if [ -f "pnpm-lock.yaml.backup" ]; then
                    echo "🔄 尝试恢复备份的lockfile..."
                    cp pnpm-lock.yaml.backup pnpm-lock.yaml
                fi
                exit 1
            fi
        else
            echo "✅ ${project_name} lockfile正常，无需修复"
        fi
    else
        echo "⚠️  ${project_name} 没有package.json，跳过"
    fi
    
    cd ..
}

# 检查是否有AWS SDK残留
check_aws_sdk_remnants() {
    echo "🔍 检查AWS SDK残留..."
    
    local found_aws=false
    
    for dir in admin chat service; do
        if [ -f "$dir/package.json" ]; then
            if grep -q "@aws-sdk" "$dir/package.json"; then
                echo "❌ 在 $dir/package.json 中发现AWS SDK依赖"
                found_aws=true
            fi
        fi
    done
    
    if [ "$found_aws" = true ]; then
        echo "⚠️  发现AWS SDK残留，请手动删除相关依赖"
        echo "或运行: ./clean-deps.sh 进行完整清理"
        return 1
    else
        echo "✅ 没有发现AWS SDK残留"
        return 0
    fi
}

# 主修复流程
echo "🔍 开始检查和修复lockfile问题..."

# 检查AWS SDK残留
if ! check_aws_sdk_remnants; then
    echo ""
    echo "建议先清理AWS SDK残留，然后重新运行此脚本"
    exit 1
fi

echo ""
echo "🔧 开始修复各项目的lockfile..."

# 修复各个项目
fix_project_lockfile "service" "后端服务"
fix_project_lockfile "admin" "管理后台"
fix_project_lockfile "chat" "用户界面"

echo ""
echo "🧹 清理临时文件..."
find . -name "pnpm-lock.yaml.backup" -delete 2>/dev/null || true

echo ""
echo "======================================="
echo "✅ Lockfile修复完成！"
echo ""
echo "现在可以运行以下命令："
echo "  ./build.sh          # 构建项目"
echo "  node pre-build-check.js  # 运行构建前检查"
echo "======================================="
