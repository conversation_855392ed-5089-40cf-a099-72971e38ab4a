#!/bin/bash

# 彻底清理AWS SDK相关内容的脚本

echo "======================================="
echo "        彻底清理AWS SDK"
echo "======================================="

# 检查pnpm
command -v pnpm >/dev/null 2>&1 || { echo "❌ 错误: 需要安装 pnpm"; exit 1; }

echo "🧹 开始彻底清理AWS SDK相关内容..."

# 1. 删除所有lockfile
echo "🗑️  删除所有lockfile..."
find . -name "pnpm-lock.yaml" -delete
find . -name "package-lock.json" -delete
find . -name "yarn.lock" -delete
echo "✅ 已删除所有lockfile"

# 2. 删除所有node_modules
echo "🗑️  删除所有node_modules..."
find . -name "node_modules" -type d -exec rm -rf {} + 2>/dev/null || true
echo "✅ 已删除所有node_modules"

# 3. 检查package.json中是否还有AWS SDK残留
echo "🔍 检查package.json中的AWS SDK残留..."
for dir in admin chat service AIWebQuickDeploy; do
    if [ -f "$dir/package.json" ]; then
        if grep -q "@aws-sdk\|aws-sdk" "$dir/package.json"; then
            echo "⚠️  在 $dir/package.json 中发现AWS SDK残留"
            echo "正在删除..."
            # 使用sed删除AWS SDK相关行
            sed -i.bak '/@aws-sdk/d' "$dir/package.json"
            sed -i.bak '/aws-sdk/d' "$dir/package.json"
            echo "✅ 已从 $dir/package.json 删除AWS SDK依赖"
        else
            echo "✅ $dir/package.json 中没有AWS SDK残留"
        fi
    fi
done

# 4. 清理pnpm缓存
echo "🧹 清理pnpm缓存..."
pnpm store prune 2>/dev/null || true
echo "✅ pnpm缓存已清理"

# 5. 重新安装所有依赖
echo "📦 重新安装所有依赖..."

# 安装service依赖
echo "📦 安装后端服务依赖..."
cd service/
if pnpm install; then
    echo "✅ 后端服务依赖安装成功"
else
    echo "❌ 后端服务依赖安装失败"
    exit 1
fi
cd ..

# 安装admin依赖
echo "📦 安装管理后台依赖..."
cd admin/
if pnpm install; then
    echo "✅ 管理后台依赖安装成功"
else
    echo "❌ 管理后台依赖安装失败"
    exit 1
fi
cd ..

# 安装chat依赖
echo "📦 安装用户界面依赖..."
cd chat/
if pnpm install; then
    echo "✅ 用户界面依赖安装成功"
else
    echo "❌ 用户界面依赖安装失败"
    exit 1
fi
cd ..

# 6. 验证没有AWS SDK残留
echo "🔍 最终验证..."
aws_found=false
for dir in admin chat service; do
    if [ -f "$dir/pnpm-lock.yaml" ]; then
        if grep -q "@aws-sdk\|aws-sdk" "$dir/pnpm-lock.yaml"; then
            echo "⚠️  在 $dir/pnpm-lock.yaml 中仍发现AWS SDK"
            aws_found=true
        fi
    fi
done

if [ "$aws_found" = true ]; then
    echo "❌ 仍有AWS SDK残留，需要手动检查"
    exit 1
else
    echo "✅ 没有发现AWS SDK残留"
fi

# 7. 测试构建
echo "🔨 测试构建..."

# 构建service
echo "🔨 构建后端服务..."
cd service/
if pnpm build; then
    echo "✅ 后端服务构建成功"
else
    echo "❌ 后端服务构建失败"
    exit 1
fi
cd ..

# 构建admin
echo "🔨 构建管理后台..."
cd admin/
if pnpm build; then
    echo "✅ 管理后台构建成功"
else
    echo "❌ 管理后台构建失败"
    exit 1
fi
cd ..

# 构建chat
echo "🔨 构建用户界面..."
cd chat/
if pnpm build; then
    echo "✅ 用户界面构建成功"
else
    echo "❌ 用户界面构建失败"
    exit 1
fi
cd ..

# 8. 清理备份文件
echo "🧹 清理备份文件..."
find . -name "*.bak" -delete 2>/dev/null || true

echo ""
echo "======================================="
echo "✅ AWS SDK彻底清理完成！"
echo ""
echo "清理内容："
echo "  ✅ 删除所有lockfile"
echo "  ✅ 删除所有node_modules"
echo "  ✅ 清理package.json中的AWS SDK依赖"
echo "  ✅ 清理pnpm缓存"
echo "  ✅ 重新安装所有依赖"
echo "  ✅ 验证构建成功"
echo ""
echo "现在可以运行完整构建："
echo "  ./build.sh"
echo "======================================="
